import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from './utils/test-utils'
import userEvent from '@testing-library/user-event'

// Import representative tools for performance testing
import RomanNumeral from '../components/tools/roman-numeral'
import CurrencyConvert from '../components/tools/currency-convert'
import MatrixMath from '../components/tools/matrix-math'
import WordCount from '../components/tools/word-count'
import JsonPretty from '../components/tools/json-pretty'
import Base64Encode from '../components/tools/base64-encode'
import PasswordGenerator from '../components/tools/password-generator'
import ColorPicker from '../components/tools/color-picker'

const performanceTools = [
  { name: 'Roman Numeral', component: RomanNumeral },
  { name: 'Currency Convert', component: CurrencyConvert },
  { name: 'Matrix Math', component: MatrixMath },
  { name: 'Word Count', component: WordCount },
  { name: 'JSON Pretty', component: JsonPretty },
  { name: 'Base64 Encode', component: Base64Encode },
  { name: 'Password Generator', component: PasswordGenerator },
  { name: 'Color Picker', component: ColorPicker },
]

describe('Performance Tests', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    // Clear performance marks
    performance.clearMarks()
    performance.clearMeasures()
  })

  describe('Rendering Performance', () => {
    it.each(performanceTools)('renders $name within performance budget', ({ component: Component }) => {
      const startTime = performance.now()

      render(<Component />)

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render within 500ms
      expect(renderTime).toBeLessThan(500)
    })

    it.each(performanceTools)('has reasonable DOM size in $name', ({ component: Component }) => {
      render(<Component />)

      const elementCount = document.querySelectorAll('*').length

      // Should not create excessive DOM elements
      expect(elementCount).toBeLessThan(1000)
    })

    it.each(performanceTools)('has reasonable component tree depth in $name', ({ component: Component }) => {
      render(<Component />)

      // Find deepest nesting level
      let maxDepth = 0
      const calculateDepth = (element: Element, depth = 0): void => {
        maxDepth = Math.max(maxDepth, depth)
        Array.from(element.children).forEach((child) => calculateDepth(child, depth + 1))
      }

      calculateDepth(document.body)

      // Should not have excessive nesting
      expect(maxDepth).toBeLessThan(50)
    })
  })

  describe('Processing Performance', () => {
    it('handles large text input efficiently in Word Count', async () => {
      render(<WordCount />)

      const largeText = 'word '.repeat(10000) // 10,000 words
      const textInput = screen.getByLabelText(/text/i)

      const startTime = performance.now()

      await user.clear(textInput)
      await user.type(textInput, largeText)

      // Wait for processing
      await waitFor(
        () => {
          expect(screen.getByText(/10,000/)).toBeInTheDocument()
        },
        { timeout: 5000 }
      )

      const endTime = performance.now()
      const processingTime = endTime - startTime

      // Should process within 3 seconds
      expect(processingTime).toBeLessThan(3000)
    })

    it('handles large JSON input efficiently in JSON Pretty', async () => {
      render(<JsonPretty />)

      // Create large JSON object
      const largeObject = {
        data: Array(1000)
          .fill(0)
          .map((_, i) => ({
            id: i,
            name: `Item ${i}`,
            value: Math.random(),
            nested: {
              property1: `value${i}`,
              property2: i * 2,
              array: [1, 2, 3, 4, 5],
            },
          })),
      }

      const jsonInput = screen.getByLabelText(/json/i)

      const startTime = performance.now()

      await user.clear(jsonInput)
      await user.type(jsonInput, JSON.stringify(largeObject))

      // Wait for formatting
      await waitFor(
        () => {
          expect(screen.getByText(/formatted/i)).toBeInTheDocument()
        },
        { timeout: 5000 }
      )

      const endTime = performance.now()
      const processingTime = endTime - startTime

      // Should process within 3 seconds
      expect(processingTime).toBeLessThan(3000)
    })

    it('handles large matrix operations efficiently in Matrix Math', async () => {
      render(<MatrixMath />)

      // Create large matrix (10x10)
      const largeMatrix = Array(10)
        .fill(0)
        .map(() => Array(10).fill(1))
      const matrixString = JSON.stringify(largeMatrix)

      const matrixInput = screen.getByLabelText(/matrix/i)

      const startTime = performance.now()

      await user.clear(matrixInput)
      await user.type(matrixInput, matrixString)

      // Trigger calculation
      const calculateButton = screen.getByRole('button', { name: /calculate/i })
      await user.click(calculateButton)

      // Wait for result
      await waitFor(
        () => {
          expect(screen.getByText(/result/i)).toBeInTheDocument()
        },
        { timeout: 5000 }
      )

      const endTime = performance.now()
      const processingTime = endTime - startTime

      // Should process within 2 seconds
      expect(processingTime).toBeLessThan(2000)
    })

    it('handles rapid input changes efficiently', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/arabic/i)

      const startTime = performance.now()

      // Rapidly change input 50 times
      for (let i = 1; i <= 50; i++) {
        await user.clear(arabicInput)
        await user.type(arabicInput, i.toString())
      }

      // Wait for final conversion
      await waitFor(() => {
        const romanInput = screen.getByLabelText(/roman/i) as HTMLInputElement
        expect(romanInput.value).toBe('L')
      })

      const endTime = performance.now()
      const processingTime = endTime - startTime

      // Should handle rapid changes within 5 seconds
      expect(processingTime).toBeLessThan(5000)
    })
  })

  describe('Memory Performance', () => {
    it.each(performanceTools)('does not create memory leaks in $name', async ({ component: Component }) => {
      // This is a basic test - in production you'd use more sophisticated tools
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0

      // Render and unmount component multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<Component />)
        unmount()
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0

      // Memory should not grow excessively (allow for some variance)
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryGrowth = finalMemory - initialMemory
        expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024) // 10MB
      }
    })

    it.each(performanceTools)('cleans up event listeners in $name', ({ component: Component }) => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener')
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')

      const { unmount } = render(<Component />)

      const addedListeners = addEventListenerSpy.mock.calls.length

      unmount()

      const removedListeners = removeEventListenerSpy.mock.calls.length

      // Should remove at least as many listeners as added
      expect(removedListeners).toBeGreaterThanOrEqual(addedListeners * 0.8) // Allow some variance

      addEventListenerSpy.mockRestore()
      removeEventListenerSpy.mockRestore()
    })

    it.each(performanceTools)('cleans up timers and intervals in $name', ({ component: Component }) => {
      const setTimeoutSpy = vi.spyOn(global, 'setTimeout')
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout')
      const setIntervalSpy = vi.spyOn(global, 'setInterval')
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval')

      const { unmount } = render(<Component />)

      const timeouts = setTimeoutSpy.mock.calls.length
      const intervals = setIntervalSpy.mock.calls.length

      unmount()

      const clearedTimeouts = clearTimeoutSpy.mock.calls.length
      const clearedIntervals = clearIntervalSpy.mock.calls.length

      // Should clean up most timers (allow for some system timers)
      expect(clearedTimeouts).toBeGreaterThanOrEqual(timeouts * 0.7)
      expect(clearedIntervals).toBeGreaterThanOrEqual(intervals)

      setTimeoutSpy.mockRestore()
      clearTimeoutSpy.mockRestore()
      setIntervalSpy.mockRestore()
      clearIntervalSpy.mockRestore()
    })
  })

  describe('Network Performance', () => {
    it('debounces API calls in Currency Convert', async () => {
      const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify({ rate: 1.2 })))

      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/amount/i)

      // Rapidly change amount
      await user.clear(amountInput)
      await user.type(amountInput, '100')
      await user.clear(amountInput)
      await user.type(amountInput, '200')
      await user.clear(amountInput)
      await user.type(amountInput, '300')

      // Wait for debounced call
      await waitFor(() => {
        // Should make fewer calls than input changes due to debouncing
        expect(fetchSpy.mock.calls.length).toBeLessThan(5)
      })

      fetchSpy.mockRestore()
    })

    it('caches repeated requests', async () => {
      const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify({ rate: 1.2 })))

      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/amount/i)

      // Make same conversion twice
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      await waitFor(() => {
        expect(screen.getByText(/result/i)).toBeInTheDocument()
      })

      await user.clear(amountInput)
      await user.type(amountInput, '200')
      await user.clear(amountInput)
      await user.type(amountInput, '100') // Same as first

      // Should not make additional request for cached data
      expect(fetchSpy.mock.calls.length).toBeLessThanOrEqual(2)

      fetchSpy.mockRestore()
    })
  })

  describe('Bundle Size Performance', () => {
    it.each(performanceTools)('has reasonable component size in $name', ({ component: Component }) => {
      // This is a proxy test - in real scenarios you'd analyze bundle size
      const componentString = Component.toString()
      const componentSize = componentString.length

      // Component should not be excessively large
      expect(componentSize).toBeLessThan(100000) // 100KB as string
    })

    it.each(performanceTools)('uses code splitting appropriately in $name', ({ component: Component }) => {
      // Check if component uses dynamic imports
      const componentString = Component.toString()

      // For large components, should consider code splitting
      if (componentString.length > 50000) {
        // Large components should consider code splitting
        // This is more of a guideline than a hard requirement
      }

      // Test passes if no errors thrown
      expect(true).toBe(true)
    })
  })

  describe('Animation Performance', () => {
    it.each(performanceTools)('uses efficient animations in $name', async ({ component: Component }) => {
      render(<Component />)

      // Look for animated elements
      const animatedElements = document.querySelectorAll('[class*="animate"], [class*="transition"]')

      animatedElements.forEach((element) => {
        const styles = window.getComputedStyle(element)

        // Should use transform/opacity for animations (GPU accelerated)
        const transition = styles.transition
        if (transition && transition !== 'none') {
          const isEfficientAnimation =
            transition.includes('transform') || transition.includes('opacity') || transition.includes('filter')

          // If animating, should prefer efficient properties
          if (
            transition.includes('width') ||
            transition.includes('height') ||
            transition.includes('top') ||
            transition.includes('left')
          ) {
            console.warn('Consider using transform instead of layout properties for animation')
          }
        }
      })

      // Test passes if no errors
      expect(true).toBe(true)
    })

    it.each(performanceTools)('respects reduced motion preferences in $name', ({ component: Component }) => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation((query) => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      })

      render(<Component />)

      // Should respect reduced motion (this is more of a guideline test)
      const animatedElements = document.querySelectorAll('[class*="animate"]')

      // In a real implementation, animations should be disabled or reduced
      // when prefers-reduced-motion: reduce is set
      expect(true).toBe(true)
    })
  })
})
