//@ts-nocheck
import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(() => Promise.resolve()),
    readText: vi.fn(() => Promise.resolve('')),
  },
})

// Mock URL.createObjectURL and URL.revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'mocked-url')
global.URL.revokeObjectURL = vi.fn()

// Mock File and FileReader
global.File = class MockFile {
  constructor(
    public chunks: any[],
    public name: string,
    public options?: any
  ) {}
  get size() {
    return 0
  }
  get type() {
    return this.options?.type || ''
  }
  get lastModified() {
    return Date.now()
  }
  get webkitRelativePath() {
    return ''
  }
  arrayBuffer() {
    return Promise.resolve(new ArrayBuffer(0))
  }
  text() {
    return Promise.resolve('')
  }
  stream() {
    return new ReadableStream()
  }
  slice() {
    return new MockFile([], '')
  }
}

global.FileReader = class MockFileReader {
  result: any = null
  error: any = null
  readyState: number = 0
  onload: any = null
  onerror: any = null
  onabort: any = null
  onloadstart: any = null
  onloadend: any = null
  onprogress: any = null

  readAsText() {
    setTimeout(() => {
      this.result = 'mocked file content'
      this.readyState = 2
      if (this.onload) this.onload({ target: this })
    }, 0)
  }

  readAsDataURL() {
    setTimeout(() => {
      this.result = 'data:text/plain;base64,bW9ja2VkIGZpbGUgY29udGVudA=='
      this.readyState = 2
      if (this.onload) this.onload({ target: this })
    }, 0)
  }

  readAsArrayBuffer() {
    setTimeout(() => {
      this.result = new ArrayBuffer(0)
      this.readyState = 2
      if (this.onload) this.onload({ target: this })
    }, 0)
  }

  abort() {
    this.readyState = 2
    if (this.onabort) this.onabort({ target: this })
  }
}

// Mock performance API
global.performance = {
  ...global.performance,
  now: vi.fn(() => Date.now()),
}

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock HTMLCanvasElement
HTMLCanvasElement.prototype.getContext = vi.fn(() => ({
  fillRect: vi.fn(),
  clearRect: vi.fn(),
  getImageData: vi.fn(() => ({ data: new Array(4) })),
  putImageData: vi.fn(),
  createImageData: vi.fn(() => ({ data: new Array(4) })),
  setTransform: vi.fn(),
  drawImage: vi.fn(),
  save: vi.fn(),
  fillText: vi.fn(),
  restore: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  closePath: vi.fn(),
  stroke: vi.fn(),
  translate: vi.fn(),
  scale: vi.fn(),
  rotate: vi.fn(),
  arc: vi.fn(),
  fill: vi.fn(),
  measureText: vi.fn(() => ({ width: 0 })),
  transform: vi.fn(),
  rect: vi.fn(),
  clip: vi.fn(),
}))

// Mock HTMLCanvasElement.toDataURL
HTMLCanvasElement.prototype.toDataURL = vi.fn(() => 'data:image/png;base64,mocked')

// Suppress console warnings during tests
const originalConsoleWarn = console.warn
console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('React Router') || args[0].includes('Warning:') || args[0].includes('validateDOMNesting'))
  ) {
    return
  }
  originalConsoleWarn(...args)
}
