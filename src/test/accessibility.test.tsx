import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen } from './utils/test-utils'
import userEvent from '@testing-library/user-event'

// Import a few representative tools for accessibility testing
import RomanNumeral from '../components/tools/roman-numeral'
import CurrencyConvert from '../components/tools/currency-convert'
import MatrixMath from '../components/tools/matrix-math'
import WordCount from '../components/tools/word-count'
import ColorPicker from '../components/tools/color-picker'

const accessibilityTools = [
  { name: 'Roman Numeral', component: RomanNumeral },
  { name: 'Currency Convert', component: CurrencyConvert },
  { name: 'Matrix Math', component: MatrixMath },
  { name: 'Word Count', component: WordCount },
  { name: 'Color Picker', component: ColorPicker },
]

describe('Accessibility Tests', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Keyboard Navigation', () => {
    it.each(accessibilityTools)('supports Tab navigation in $name', async ({ component: Component }) => {
      render(<Component />)

      // Find all focusable elements
      const focusableElements = screen
        .queryAllByRole('button')
        .concat(screen.queryAllByRole('textbox'))
        .concat(screen.queryAllByRole('combobox'))
        .concat(screen.queryAllByRole('tab'))
        .filter((el) => !el.hasAttribute('disabled') && !el.hasAttribute('aria-hidden'))

      if (focusableElements.length > 1) {
        // Focus first element
        focusableElements[0].focus()
        expect(focusableElements[0]).toHaveFocus()

        // Tab through elements
        for (let i = 1; i < Math.min(focusableElements.length, 5); i++) {
          await user.tab()
          // Should move focus to next element
          expect(document.activeElement).toBeTruthy()
        }
      }
    })

    it.each(accessibilityTools)('supports Shift+Tab reverse navigation in $name', async ({ component: Component }) => {
      render(<Component />)

      const focusableElements = screen
        .queryAllByRole('button')
        .concat(screen.queryAllByRole('textbox'))
        .filter((el) => !el.hasAttribute('disabled'))

      if (focusableElements.length > 1) {
        // Focus last element
        focusableElements[focusableElements.length - 1].focus()

        // Shift+Tab to previous element
        await user.tab({ shift: true })
        expect(document.activeElement).toBeTruthy()
      }
    })

    it.each(accessibilityTools)('supports Enter/Space activation in $name', async ({ component: Component }) => {
      render(<Component />)

      const buttons = screen.queryAllByRole('button').filter((btn) => !btn.hasAttribute('disabled'))

      if (buttons.length > 0) {
        const button = buttons[0]
        button.focus()

        // Test Enter key
        await user.keyboard('{Enter}')
        // Should not throw error

        // Test Space key
        await user.keyboard(' ')
        // Should not throw error
      }
    })

    it.each(accessibilityTools)('supports Arrow key navigation for tabs in $name', async ({ component: Component }) => {
      render(<Component />)

      const tabs = screen.queryAllByRole('tab')

      if (tabs.length > 1) {
        tabs[0].focus()

        // Arrow right to next tab
        await user.keyboard('{ArrowRight}')
        expect(document.activeElement).toBeTruthy()

        // Arrow left to previous tab
        await user.keyboard('{ArrowLeft}')
        expect(document.activeElement).toBeTruthy()
      }
    })

    it.each(accessibilityTools)('supports Escape key for modals/dialogs in $name', async ({ component: Component }) => {
      render(<Component />)

      // Look for modal triggers
      const modalTriggers = screen
        .queryAllByRole('button')
        .filter(
          (btn) =>
            btn.textContent?.toLowerCase().includes('help') ||
            btn.textContent?.toLowerCase().includes('info') ||
            btn.textContent?.toLowerCase().includes('settings')
        )

      if (modalTriggers.length > 0) {
        await user.click(modalTriggers[0])

        // Press Escape
        await user.keyboard('{Escape}')
        // Should close modal (no error)
      }
    })
  })

  describe('ARIA Labels and Roles', () => {
    it.each(accessibilityTools)('has proper ARIA labels for inputs in $name', ({ component: Component }) => {
      render(<Component />)

      const inputs = screen.queryAllByRole('textbox')
      const selects = screen.queryAllByRole('combobox')
      const buttons = screen.queryAllByRole('button')

      const allInteractiveElements = [...inputs, ...selects, ...buttons]

      allInteractiveElements.forEach((element) => {
        const hasLabel =
          element.hasAttribute('aria-label') ||
          element.hasAttribute('aria-labelledby') ||
          (element.hasAttribute('id') && document.querySelector(`label[for="${element.id}"]`))

        if (!hasLabel) {
          // Some elements might have accessible names through other means
          const accessibleName =
            element.textContent || element.getAttribute('title') || element.getAttribute('placeholder')
          expect(accessibleName).toBeTruthy()
        }
      })
    })

    it.each(accessibilityTools)('has proper heading hierarchy in $name', ({ component: Component }) => {
      render(<Component />)

      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))

      if (headings.length > 0) {
        // Should start with h1 or h2 (since it's in a page context)
        const firstHeading = headings[0]
        const level = parseInt(firstHeading.tagName.charAt(1))
        expect(level).toBeLessThanOrEqual(2)

        // Check hierarchy doesn't skip levels
        for (let i = 1; i < headings.length; i++) {
          const currentLevel = parseInt(headings[i].tagName.charAt(1))
          const previousLevel = parseInt(headings[i - 1].tagName.charAt(1))
          expect(currentLevel - previousLevel).toBeLessThanOrEqual(1)
        }
      }
    })

    it.each(accessibilityTools)('has proper ARIA roles for custom components in $name', ({ component: Component }) => {
      render(<Component />)

      // Check for custom components that should have roles
      const customElements = document.querySelectorAll('[class*="tab"], [class*="dialog"], [class*="menu"]')

      customElements.forEach((element) => {
        if (element.classList.contains('tab')) {
          expect(element.getAttribute('role')).toBe('tab')
        }
        if (element.classList.contains('dialog')) {
          expect(element.getAttribute('role')).toBe('dialog')
        }
        if (element.classList.contains('menu')) {
          expect(element.getAttribute('role')).toBe('menu')
        }
      })
    })

    it.each(accessibilityTools)('has proper ARIA states and properties in $name', ({ component: Component }) => {
      render(<Component />)

      // Check tabs have proper ARIA states
      const tabs = screen.queryAllByRole('tab')
      tabs.forEach((tab) => {
        expect(tab.hasAttribute('aria-selected')).toBe(true)
        if (tab.getAttribute('aria-selected') === 'true') {
          expect(tab.hasAttribute('aria-controls')).toBe(true)
        }
      })

      // Check buttons have proper states
      const buttons = screen.queryAllByRole('button')
      buttons.forEach((button) => {
        if (button.hasAttribute('aria-expanded')) {
          const expanded = button.getAttribute('aria-expanded')
          expect(['true', 'false'].includes(expanded!)).toBe(true)
        }
      })
    })
  })

  describe('Screen Reader Support', () => {
    it.each(accessibilityTools)('has skip links for screen readers in $name', ({ component: Component }) => {
      render(<Component />)

      const skipLink = screen.getByText(/skip to main content/i)
      expect(skipLink).toBeInTheDocument()
      expect(skipLink).toHaveClass('sr-only')

      // Skip link should become visible on focus
      skipLink.focus()
      expect(skipLink).toHaveClass('focus:not-sr-only')
    })

    it.each(accessibilityTools)('announces dynamic content changes in $name', async ({ component: Component }) => {
      render(<Component />)

      // Look for live regions
      const liveRegions = document.querySelectorAll('[aria-live], [role="status"], [role="alert"]')

      // Should have at least one live region for announcements
      expect(liveRegions.length).toBeGreaterThan(0)
    })

    it.each(accessibilityTools)(
      'has descriptive text for complex interactions in $name',
      ({ component: Component }) => {
        render(<Component />)

        // Check for aria-describedby attributes
        const describedElements = document.querySelectorAll('[aria-describedby]')

        describedElements.forEach((element) => {
          const describedBy = element.getAttribute('aria-describedby')
          const description = document.getElementById(describedBy!)
          expect(description).toBeInTheDocument()
          expect(description?.textContent).toBeTruthy()
        })
      }
    )

    it.each(accessibilityTools)('provides context for form errors in $name', async ({ component: Component }) => {
      render(<Component />)

      // Look for form inputs
      const inputs = screen.queryAllByRole('textbox')

      if (inputs.length > 0) {
        const input = inputs[0]

        // Try to trigger validation error
        await user.clear(input)
        await user.type(input, 'invalid input that should cause error')
        await user.tab() // Blur to trigger validation

        // Check if error is properly associated
        const errorId = input.getAttribute('aria-describedby')
        if (errorId) {
          const errorElement = document.getElementById(errorId)
          expect(errorElement).toBeInTheDocument()
        }
      }
    })
  })

  describe('Focus Management', () => {
    it.each(accessibilityTools)('maintains focus visibility in $name', ({ component: Component }) => {
      render(<Component />)

      const focusableElements = screen
        .queryAllByRole('button')
        .concat(screen.queryAllByRole('textbox'))
        .filter((el) => !el.hasAttribute('disabled'))

      if (focusableElements.length > 0) {
        focusableElements[0].focus()

        // Check focus is visible (not hidden by CSS)
        const focusedElement = document.activeElement as HTMLElement
        const styles = window.getComputedStyle(focusedElement)

        // Should not have outline: none without alternative focus indicator
        if (styles.outline === 'none' || styles.outline === '0px') {
          // Should have alternative focus indicator
          expect(
            styles.boxShadow !== 'none' || styles.border !== 'none' || styles.backgroundColor !== 'transparent'
          ).toBe(true)
        }
      }
    })

    it.each(accessibilityTools)('traps focus in modals in $name', async ({ component: Component }) => {
      render(<Component />)

      // Look for modal triggers
      const modalTriggers = screen
        .queryAllByRole('button')
        .filter(
          (btn) =>
            btn.textContent?.toLowerCase().includes('help') || btn.textContent?.toLowerCase().includes('settings')
        )

      if (modalTriggers.length > 0) {
        await user.click(modalTriggers[0])

        // Check if modal is open
        const modal = document.querySelector('[role="dialog"]')
        if (modal) {
          // Focus should be trapped within modal
          const modalFocusableElements = modal.querySelectorAll(
            'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
          )

          if (modalFocusableElements.length > 0) {
            // First element should be focused
            expect(document.activeElement).toBe(modalFocusableElements[0])
          }
        }
      }
    })

    it.each(accessibilityTools)('restores focus after modal close in $name', async ({ component: Component }) => {
      render(<Component />)

      const modalTriggers = screen
        .queryAllByRole('button')
        .filter((btn) => btn.textContent?.toLowerCase().includes('help'))

      if (modalTriggers.length > 0) {
        const trigger = modalTriggers[0]

        // Focus and open modal
        trigger.focus()
        await user.click(trigger)

        // Close modal (if it opened)
        await user.keyboard('{Escape}')

        // Focus should return to trigger
        expect(document.activeElement).toBe(trigger)
      }
    })
  })

  describe('Color and Contrast', () => {
    it.each(accessibilityTools)('has sufficient color contrast in $name', ({ component: Component }) => {
      render(<Component />)

      // This is a basic check - in a real app you'd use tools like axe-core
      const textElements = document.querySelectorAll('p, span, div, label, button')

      textElements.forEach((element) => {
        const styles = window.getComputedStyle(element)
        const color = styles.color
        const backgroundColor = styles.backgroundColor

        // Basic check that text isn't invisible
        expect(color).not.toBe(backgroundColor)
        expect(color).not.toBe('transparent')
      })
    })

    it.each(accessibilityTools)(
      'does not rely solely on color for information in $name',
      ({ component: Component }) => {
        render(<Component />)

        // Check for error states that might rely on color
        const errorElements = document.querySelectorAll('[class*="error"], [class*="danger"], [class*="red"]')

        errorElements.forEach((element) => {
          // Should have text content or icons, not just color
          const hasTextContent = element.textContent && element.textContent.trim().length > 0
          const hasIcon = element.querySelector('svg, [class*="icon"]')
          const hasAriaLabel = element.hasAttribute('aria-label')

          expect(hasTextContent || hasIcon || hasAriaLabel).toBe(true)
        })
      }
    )
  })

  describe('Responsive and Mobile Accessibility', () => {
    it.each(accessibilityTools)('maintains accessibility on mobile viewports in $name', ({ component: Component }) => {
      // Simulate mobile viewport
      Object.defineProperty(window, 'innerWidth', { value: 375 })
      Object.defineProperty(window, 'innerHeight', { value: 667 })

      render(<Component />)

      // Check that interactive elements are large enough for touch
      const buttons = screen.queryAllByRole('button')

      buttons.forEach((button) => {
        const rect = button.getBoundingClientRect()
        // Minimum touch target size should be 44x44px
        expect(rect.width).toBeGreaterThanOrEqual(32) // Slightly relaxed for testing
        expect(rect.height).toBeGreaterThanOrEqual(32)
      })
    })

    it.each(accessibilityTools)('supports zoom up to 200% in $name', ({ component: Component }) => {
      render(<Component />)

      // Simulate zoom by changing font size
      document.documentElement.style.fontSize = '32px' // 200% of 16px

      // Content should still be accessible
      const textElements = document.querySelectorAll('p, span, div, label')
      textElements.forEach((element) => {
        const rect = element.getBoundingClientRect()
        // Text should not be clipped
        expect(rect.width).toBeGreaterThan(0)
        expect(rect.height).toBeGreaterThan(0)
      })

      // Reset
      document.documentElement.style.fontSize = ''
    })
  })
})
