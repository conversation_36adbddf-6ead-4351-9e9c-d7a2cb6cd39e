import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { RouterProvider, createMemoryHistory, createRouter } from '@tanstack/react-router'
import { routeTree } from '../../routeTree.gen'

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
})

// Create a test router
const createTestRouter = (initialEntries: string[] = ['/']) => {
  const history = createMemoryHistory({
    initialEntries,
  })

  return createRouter({
    routeTree,
    history,
    context: {
      queryClient: createTestQueryClient(),
    },
  })
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[]
  queryClient?: QueryClient
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries = ['/'], queryClient = createTestQueryClient(), ...renderOptions } = options

  const router = createTestRouter(initialEntries)

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router}>
        {children}
      </RouterProvider>
    </QueryClientProvider>
  )

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
    router,
  }
}

// Tool-specific test utilities
export const createMockFile = (name: string, content: string, type: string = 'text/plain') => {
  const file = new File([content], name, { type })
  return file
}

export const createMockImageFile = (name: string = 'test.jpg') => {
  const canvas = document.createElement('canvas')
  canvas.width = 100
  canvas.height = 100
  const ctx = canvas.getContext('2d')
  if (ctx) {
    ctx.fillStyle = 'red'
    ctx.fillRect(0, 0, 100, 100)
  }
  
  return new Promise<File>((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], name, { type: 'image/jpeg' })
        resolve(file)
      }
    }, 'image/jpeg')
  })
}

export const waitForElement = (selector: string, timeout: number = 5000) => {
  return new Promise<Element>((resolve, reject) => {
    const startTime = Date.now()
    
    const checkElement = () => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
      } else if (Date.now() - startTime > timeout) {
        reject(new Error(`Element ${selector} not found within ${timeout}ms`))
      } else {
        setTimeout(checkElement, 100)
      }
    }
    
    checkElement()
  })
}

export const mockClipboard = () => {
  const mockWriteText = vi.fn(() => Promise.resolve())
  const mockReadText = vi.fn(() => Promise.resolve(''))
  
  Object.assign(navigator, {
    clipboard: {
      writeText: mockWriteText,
      readText: mockReadText,
    },
  })
  
  return { mockWriteText, mockReadText }
}

export const mockFileDownload = () => {
  const mockCreateObjectURL = vi.fn(() => 'mocked-url')
  const mockRevokeObjectURL = vi.fn()
  const mockClick = vi.fn()
  
  global.URL.createObjectURL = mockCreateObjectURL
  global.URL.revokeObjectURL = mockRevokeObjectURL
  
  // Mock document.createElement for download links
  const originalCreateElement = document.createElement
  document.createElement = vi.fn((tagName: string) => {
    if (tagName === 'a') {
      const element = originalCreateElement.call(document, tagName) as HTMLAnchorElement
      element.click = mockClick
      return element
    }
    return originalCreateElement.call(document, tagName)
  })
  
  return { mockCreateObjectURL, mockRevokeObjectURL, mockClick }
}

// Test data generators
export const generateTestData = {
  text: (length: number = 100) => 'a'.repeat(length),
  json: () => ({ test: 'data', number: 42, array: [1, 2, 3] }),
  csv: () => 'name,age,city\nJohn,30,New York\nJane,25,Los Angeles',
  xml: () => '<?xml version="1.0"?><root><item>test</item></root>',
  base64: () => 'SGVsbG8gV29ybGQ=', // "Hello World" in base64
  url: () => 'https://example.com/path?param=value#hash',
  email: () => '<EMAIL>',
  uuid: () => '123e4567-e89b-12d3-a456-************',
  romanNumeral: () => ({ arabic: 1984, roman: 'MCMLXXXIV' }),
  currency: () => ({ amount: 100, from: 'USD', to: 'EUR' }),
  matrix: () => [[1, 2], [3, 4]],
  color: () => '#ff0000',
  password: () => 'TestPassword123!',
  markdown: () => '# Test\n\nThis is **bold** text.',
  regex: () => '/test/gi',
  timestamp: () => 1640995200, // 2022-01-01 00:00:00 UTC
  coordinates: () => ({ lat: 40.7128, lng: -74.0060 }), // New York
}

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void) => {
  const start = performance.now()
  await fn()
  const end = performance.now()
  return end - start
}

export const measureMemory = () => {
  if ('memory' in performance) {
    return (performance as any).memory
  }
  return null
}

// Accessibility testing utilities
export const checkAccessibility = (element: Element) => {
  const checks = {
    hasAriaLabel: element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby'),
    hasRole: element.hasAttribute('role'),
    isFocusable: element.hasAttribute('tabindex') || ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase()),
    hasKeyboardSupport: element.hasAttribute('onkeydown') || element.hasAttribute('onkeyup') || element.hasAttribute('onkeypress'),
  }
  
  return checks
}

// Export everything
export * from '@testing-library/react'
export * from '@testing-library/user-event'
export { customRender as render }
export { vi } from 'vitest'
