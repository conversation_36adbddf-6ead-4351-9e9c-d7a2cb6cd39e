import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'

// Import all enhanced tools
import WordCount from '../../components/tools/word-count'
import CaseConvert from '../../components/tools/char-case'
import Slugify from '../../components/tools/slugify'
import LoremIpsum from '../../components/tools/lorem-ipsum'
import MarkdownPreview from '../../components/tools/markdown-preview'
import RegexTester from '../../components/tools/regex-tester'
import DiffViewer from '../../components/tools/diff-viewer'
import TextToPdf from '../../components/tools/text-to-pdf'
import TableSorter from '../../components/tools/table-sorter'
import MarkdownToc from '../../components/tools/markdown-toc'
import ColorPicker from '../../components/tools/color-picker'
import HexRgbConverter from '../../components/tools/hex-rgb'
import GradientMaker from '../../components/tools/gradient-maker'
import ShadowGenerator from '../../components/tools/shadow-generator'
import BorderRadius from '../../components/tools/border-radius'
import Base64Encode from '../../components/tools/base64-encode'
import UrlEncode from '../../components/tools/url-encode'
import FaviconGenerator from '../../components/tools/favicon-generator'
import CssClamp from '../../components/tools/css-clamp'
import RandomColor from '../../components/tools/random-color'
import ExifViewer from '../../components/tools/exif-viewer'
import SvgMinify from '../../components/tools/svg-minify'
import Md5Hash from '../../components/tools/md5-hash'
import Sha256Hash from '../../components/tools/sha256-hash'
import BcryptHash from '../../components/tools/bcrypt-hash'
import FileHash from '../../components/tools/file-hash'
import PasswordGenerator from '../../components/tools/password-generator'
import UnixTimestamp from '../../components/tools/unix-timestamp'
import CronParser from '../../components/tools/cron-parser'
import TimeDiff from '../../components/tools/time-diff'
import TimezoneConvert from '../../components/tools/timezone-convert'
import JsonPretty from '../../components/tools/json-pretty'
import YamlToJson from '../../components/tools/yaml-to-json'
import JsonToTypescript from '../../components/tools/json-to-ts'
import CsvToJson from '../../components/tools/csv-to-json'
import ExcelToJson from '../../components/tools/excel-to-json'
import Base64ToImage from '../../components/tools/base64-image'
import HtmlPreview from '../../components/tools/html-preview'
import UserAgentAnalysis from '../../components/tools/user-agent'
import MimeTypeSearch from '../../components/tools/mime-search'
import DnsLookup from '../../components/tools/dns-lookup'
import IpInfo from '../../components/tools/ip-info'
import UrlParser from '../../components/tools/url-parser'
import PdfMerge from '../../components/tools/merge-pdf'
import UuidGenerator from '../../components/tools/uuid-generator'
import UuidBatch from '../../components/tools/uuid-batch'
import QrGenerator from '../../components/tools/qr-generator'
import BarcodeGenerator from '../../components/tools/barcode-generator'
import FakeUserGenerator from '../../components/tools/fake-user'
import LotteryPicker from '../../components/tools/lottery-picker'
import JwtDecode from '../../components/tools/jwt-decode'
import JwtGenerator from '../../components/tools/jwt-generator'
import RegexCheatsheet from '../../components/tools/regex-cheatsheet'
import JsonDiff from '../../components/tools/json-diff'
import JsonPlot from '../../components/tools/json-plot'
import MarkdownMermaid from '../../components/tools/markdown-mermaid'
import PrimeChecker from '../../components/tools/prime-checker'
import QuadraticSolver from '../../components/tools/quadratic-solver'
import MatrixMath from '../../components/tools/matrix-math'
import CurrencyConvert from '../../components/tools/currency-convert'
import RomanNumeral from '../../components/tools/roman-numeral'

// Tool definitions for testing
const tools = [
  { name: 'Word Count', component: WordCount, category: 'Text Processing' },
  { name: 'Case Convert', component: CaseConvert, category: 'Text Processing' },
  { name: 'Slugify', component: Slugify, category: 'Text Processing' },
  { name: 'Lorem Ipsum', component: LoremIpsum, category: 'Text Processing' },
  { name: 'Markdown Preview', component: MarkdownPreview, category: 'Document Tools' },
  { name: 'Regex Tester', component: RegexTester, category: 'Document Tools' },
  { name: 'Diff Viewer', component: DiffViewer, category: 'Document Tools' },
  { name: 'Text to PDF', component: TextToPdf, category: 'Document Tools' },
  { name: 'Table Sorter', component: TableSorter, category: 'Document Tools' },
  { name: 'Markdown TOC', component: MarkdownToc, category: 'Document Tools' },
  { name: 'Color Picker', component: ColorPicker, category: 'Design Tools' },
  { name: 'HEX-RGB Converter', component: HexRgbConverter, category: 'Design Tools' },
  { name: 'Gradient Maker', component: GradientMaker, category: 'Design Tools' },
  { name: 'Shadow Generator', component: ShadowGenerator, category: 'Design Tools' },
  { name: 'Border Radius', component: BorderRadius, category: 'Design Tools' },
  { name: 'Base64 Encode', component: Base64Encode, category: 'Encoding Tools' },
  { name: 'URL Encode', component: UrlEncode, category: 'Encoding Tools' },
  { name: 'Favicon Generator', component: FaviconGenerator, category: 'Web Development' },
  { name: 'CSS Clamp', component: CssClamp, category: 'Web Development' },
  { name: 'Random Color', component: RandomColor, category: 'Color Tools' },
  { name: 'EXIF Viewer', component: ExifViewer, category: 'Photography Tools' },
  { name: 'SVG Minify', component: SvgMinify, category: 'SVG Tools' },
  { name: 'MD5 Hash', component: Md5Hash, category: 'Cryptographic Tools' },
  { name: 'SHA-256 Hash', component: Sha256Hash, category: 'Cryptographic Tools' },
  { name: 'Bcrypt Hash', component: BcryptHash, category: 'Cryptographic Tools' },
  { name: 'File Hash', component: FileHash, category: 'Cryptographic Tools' },
  { name: 'Password Generator', component: PasswordGenerator, category: 'Security Tools' },
  { name: 'Unix Timestamp', component: UnixTimestamp, category: 'Time Tools' },
  { name: 'Cron Parser', component: CronParser, category: 'Time Tools' },
  { name: 'Time Diff', component: TimeDiff, category: 'Time Tools' },
  { name: 'Timezone Convert', component: TimezoneConvert, category: 'Time Tools' },
  { name: 'JSON Pretty', component: JsonPretty, category: 'Data Processing' },
  { name: 'YAML to JSON', component: YamlToJson, category: 'Data Processing' },
  { name: 'JSON to TypeScript', component: JsonToTypescript, category: 'Data Processing' },
  { name: 'CSV to JSON', component: CsvToJson, category: 'Data Processing' },
  { name: 'Excel to JSON', component: ExcelToJson, category: 'Data Processing' },
  { name: 'Base64 to Image', component: Base64ToImage, category: 'Image Processing' },
  { name: 'HTML Preview', component: HtmlPreview, category: 'Web Development' },
  { name: 'User Agent Analysis', component: UserAgentAnalysis, category: 'Browser Analysis' },
  { name: 'MIME Type Search', component: MimeTypeSearch, category: 'File Type Tools' },
  { name: 'DNS Lookup', component: DnsLookup, category: 'Network Tools' },
  { name: 'IP Info', component: IpInfo, category: 'IP Analysis' },
  { name: 'URL Parser', component: UrlParser, category: 'URL Analysis' },
  { name: 'PDF Merge', component: PdfMerge, category: 'PDF Processing' },
  { name: 'UUID Generator', component: UuidGenerator, category: 'ID Generation' },
  { name: 'UUID Batch', component: UuidBatch, category: 'Batch Processing' },
  { name: 'QR Generator', component: QrGenerator, category: 'QR Code Tools' },
  { name: 'Barcode Generator', component: BarcodeGenerator, category: 'Barcode Tools' },
  { name: 'Fake User Generator', component: FakeUserGenerator, category: 'User Data Tools' },
  { name: 'Lottery Picker', component: LotteryPicker, category: 'Selection Tools' },
  { name: 'JWT Decode', component: JwtDecode, category: 'Authentication Tools' },
  { name: 'JWT Generator', component: JwtGenerator, category: 'Token Generation' },
  { name: 'Regex Cheatsheet', component: RegexCheatsheet, category: 'Reference Tools' },
  { name: 'JSON Diff', component: JsonDiff, category: 'Comparison Tools' },
  { name: 'JSON Plot', component: JsonPlot, category: 'Visualization Tools' },
  { name: 'Markdown Mermaid', component: MarkdownMermaid, category: 'Diagram Tools' },
  { name: 'Prime Checker', component: PrimeChecker, category: 'Mathematical Tools' },
  { name: 'Quadratic Solver', component: QuadraticSolver, category: 'Equation Tools' },
  { name: 'Matrix Math', component: MatrixMath, category: 'Linear Algebra Tools' },
  { name: 'Currency Convert', component: CurrencyConvert, category: 'Financial Tools' },
  { name: 'Roman Numeral', component: RomanNumeral, category: 'Number System Tools' },
]

describe('All Enhanced Tools Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Tool Rendering', () => {
    it.each(tools)('renders $name tool without errors', ({ component: Component }) => {
      expect(() => render(<Component />)).not.toThrow()
    })

    it.each(tools)('has proper title for $name', ({ name, component: Component }) => {
      render(<Component />)
      // Each tool should have a descriptive title
      expect(document.querySelector('h1, h2, h3')).toBeInTheDocument()
    })
  })

  describe('Common Features', () => {
    it.each(tools)('has tab navigation in $name', ({ component: Component }) => {
      render(<Component />)
      const tabs = screen.queryAllByRole('tab')
      expect(tabs.length).toBeGreaterThan(0)
    })

    it.each(tools)('has copy functionality in $name', ({ component: Component }) => {
      render(<Component />)
      const copyButtons = screen.queryAllByRole('button', { name: /copy/i })
      expect(copyButtons.length).toBeGreaterThan(0)
    })

    it.each(tools)('has export functionality in $name', ({ component: Component }) => {
      render(<Component />)
      const exportButtons = screen.queryAllByRole('button', { name: /(export|download|json|csv)/i })
      expect(exportButtons.length).toBeGreaterThan(0)
    })

    it.each(tools)('has settings or help in $name', ({ component: Component }) => {
      render(<Component />)
      const settingsTab = screen.queryByRole('tab', { name: /settings/i })
      const helpContent = screen.queryByText(/help|guide|instructions/i)
      expect(settingsTab || helpContent).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it.each(tools)('has skip link in $name', ({ component: Component }) => {
      render(<Component />)
      const skipLink = screen.getByText(/skip to main content/i)
      expect(skipLink).toBeInTheDocument()
      expect(skipLink).toHaveClass('sr-only')
    })

    it.each(tools)('has proper ARIA labels in $name', ({ component: Component }) => {
      render(<Component />)
      const inputs = screen.queryAllByRole('textbox')
      const buttons = screen.queryAllByRole('button')
      const selects = screen.queryAllByRole('combobox')

      // At least some interactive elements should have labels
      const labeledElements = [...inputs, ...buttons, ...selects].filter(
        (el) =>
          el.hasAttribute('aria-label') ||
          el.hasAttribute('aria-labelledby') ||
          (el.hasAttribute('id') && document.querySelector(`label[for="${el.id}"]`))
      )

      expect(labeledElements.length).toBeGreaterThan(0)
    })

    it.each(tools)('supports keyboard navigation in $name', async ({ component: Component }) => {
      render(<Component />)

      // Find first focusable element
      const focusableElements = screen
        .queryAllByRole('button')
        .concat(screen.queryAllByRole('textbox'))
        .concat(screen.queryAllByRole('combobox'))
        .filter((el) => !el.hasAttribute('disabled'))

      if (focusableElements.length > 0) {
        focusableElements[0].focus()
        expect(focusableElements[0]).toHaveFocus()

        // Tab to next element
        await user.tab()
        expect(document.activeElement).not.toBe(focusableElements[0])
      }
    })
  })

  describe('Error Handling', () => {
    it.each(tools)('handles errors gracefully in $name', ({ component: Component }) => {
      // Mock console.error to avoid noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => render(<Component />)).not.toThrow()

      consoleSpy.mockRestore()
    })

    it.each(tools)('has error boundaries in $name', ({ component: Component }) => {
      // This test ensures error boundaries are present
      render(<Component />)

      // Look for error boundary indicators
      const errorBoundaryElements = document.querySelectorAll('[data-testid*="error"], .error-boundary')
      // Error boundaries should be present but not visible unless there's an error
      expect(errorBoundaryElements.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('Performance', () => {
    it.each(tools)('renders $name within performance budget', async ({ component: Component }) => {
      const startTime = performance.now()
      render(<Component />)
      const endTime = performance.now()

      // Should render within 1 second
      expect(endTime - startTime).toBeLessThan(1000)
    })

    it.each(tools)('has reasonable DOM size in $name', ({ component: Component }) => {
      render(<Component />)

      // Count DOM elements
      const elementCount = document.querySelectorAll('*').length

      // Should not create excessive DOM elements (reasonable limit)
      expect(elementCount).toBeLessThan(1000)
    })
  })

  describe('Consistency', () => {
    it.each(tools)('uses consistent UI components in $name', ({ component: Component }) => {
      render(<Component />)

      // Check for consistent UI patterns
      const cards = document.querySelectorAll('[class*="card"]')
      const buttons = document.querySelectorAll('[class*="button"], button')
      const inputs = document.querySelectorAll('[class*="input"], input')

      // Should use design system components
      expect(cards.length + buttons.length + inputs.length).toBeGreaterThan(0)
    })

    it.each(tools)('has consistent spacing and layout in $name', ({ component: Component }) => {
      render(<Component />)

      // Check for consistent spacing classes
      const spacingElements = document.querySelectorAll(
        '[class*="space-"], [class*="gap-"], [class*="p-"], [class*="m-"]'
      )

      // Should use consistent spacing
      expect(spacingElements.length).toBeGreaterThan(0)
    })
  })
})
