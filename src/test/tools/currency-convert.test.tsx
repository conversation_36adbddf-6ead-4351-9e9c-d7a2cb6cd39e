import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, mockFileDownload, mockClipboard } from '../utils/test-utils'
import CurrencyConvert from '../../components/tools/currency-convert'
import userEvent from '@testing-library/user-event'

describe('Currency Convert Tool', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Basic Functionality', () => {
    it('renders the Currency Convert tool', () => {
      render(<CurrencyConvert />)
      expect(screen.getByText('Currency Convert & Financial Exchange Tool')).toBeInTheDocument()
    })

    it('converts currencies', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      // Select currencies
      const fromSelect = screen.getByDisplayValue(/USD/i)
      await user.click(fromSelect)
      await user.click(screen.getByText(/EUR/i))

      // Should show conversion result
      await waitFor(() => {
        expect(screen.getByText(/Conversion Result/i)).toBeInTheDocument()
      })
    })

    it('swaps currencies', async () => {
      render(<CurrencyConvert />)

      // Click swap button
      const swapButton = screen.getByRole('button', { name: /swap/i })
      await user.click(swapButton)

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/Currencies swapped/i)).toBeInTheDocument()
      })
    })

    it('validates amount input', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '-100')

      // Should show error for negative amount
      await waitFor(() => {
        expect(screen.getByText(/Amount cannot be negative/i)).toBeInTheDocument()
      })
    })

    it('handles invalid amount', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, 'invalid')

      // Should show error for invalid amount
      await waitFor(() => {
        expect(screen.getByText(/Amount must be a valid number/i)).toBeInTheDocument()
      })
    })
  })

  describe('Advanced Features', () => {
    it('shows live exchange rates', async () => {
      render(<CurrencyConvert />)

      // Switch to rates tab
      await user.click(screen.getByRole('tab', { name: /Rates/i }))

      await waitFor(() => {
        expect(screen.getByText(/Live Exchange Rates/i)).toBeInTheDocument()
      })
    })

    it('applies currency templates', async () => {
      render(<CurrencyConvert />)

      // Switch to templates tab
      await user.click(screen.getByRole('tab', { name: /Templates/i }))

      // Click on a template
      await user.click(screen.getByText(/USD to EUR/i))

      // Should apply template
      await waitFor(() => {
        expect(screen.getByText(/Applied template/i)).toBeInTheDocument()
      })
    })

    it('manages conversion history', async () => {
      render(<CurrencyConvert />)

      // Perform a conversion
      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      // Wait for conversion
      await waitFor(() => {
        expect(screen.getByText(/Conversion Result/i)).toBeInTheDocument()
      })

      // Switch to history tab
      await user.click(screen.getByRole('tab', { name: /History/i }))

      await waitFor(() => {
        expect(screen.getByText(/conversion/i)).toBeInTheDocument()
      })
    })

    it('exports conversion data', async () => {
      const { mockCreateObjectURL, mockClick } = mockFileDownload()

      render(<CurrencyConvert />)

      // Perform a conversion
      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      // Wait for conversion
      await waitFor(() => {
        expect(screen.getByText(/Conversion Result/i)).toBeInTheDocument()
      })

      // Click export button
      const exportButton = screen.getByRole('button', { name: /JSON/i })
      await user.click(exportButton)

      expect(mockCreateObjectURL).toHaveBeenCalled()
      expect(mockClick).toHaveBeenCalled()
    })

    it('copies conversion result', async () => {
      const { mockWriteText } = mockClipboard()

      render(<CurrencyConvert />)

      // Perform a conversion
      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      // Wait for conversion
      await waitFor(() => {
        expect(screen.getByText(/Conversion Result/i)).toBeInTheDocument()
      })

      // Click copy button
      const copyButton = screen.getByRole('button', { name: /copy/i })
      await user.click(copyButton)

      expect(mockWriteText).toHaveBeenCalled()
    })
  })

  describe('Currency Support', () => {
    it('supports major currencies', () => {
      render(<CurrencyConvert />)

      // Switch to settings tab
      user.click(screen.getByRole('tab', { name: /Settings/i }))

      // Should show supported currencies
      expect(screen.getByText(/US Dollar/i)).toBeInTheDocument()
      expect(screen.getByText(/Euro/i)).toBeInTheDocument()
      expect(screen.getByText(/British Pound/i)).toBeInTheDocument()
    })

    it('supports cryptocurrencies', () => {
      render(<CurrencyConvert />)

      // Switch to settings tab
      user.click(screen.getByRole('tab', { name: /Settings/i }))

      // Should show cryptocurrencies
      expect(screen.getByText(/Bitcoin/i)).toBeInTheDocument()
      expect(screen.getByText(/Ethereum/i)).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<CurrencyConvert />)

      expect(screen.getByLabelText(/Amount/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/From Currency/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/To Currency/i)).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)
      amountInput.focus()

      // Tab to next element
      await user.tab()
      expect(screen.getByLabelText(/From Currency/i)).toHaveFocus()
    })

    it('has skip link for screen readers', () => {
      render(<CurrencyConvert />)

      const skipLink = screen.getByText(/Skip to main content/i)
      expect(skipLink).toBeInTheDocument()
      expect(skipLink).toHaveClass('sr-only')
    })
  })

  describe('Error Handling', () => {
    it('handles network errors gracefully', async () => {
      // Mock fetch to simulate network error
      global.fetch = vi.fn(() => Promise.reject(new Error('Network error')))

      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)
      await user.clear(amountInput)
      await user.type(amountInput, '100')

      // Should handle error gracefully
      await waitFor(() => {
        expect(screen.getByText(/failed/i)).toBeInTheDocument()
      })
    })

    it('validates currency selection', async () => {
      render(<CurrencyConvert />)

      // Try to convert with same currencies
      const fromSelect = screen.getByDisplayValue(/USD/i)
      await user.click(fromSelect)
      await user.click(screen.getByText(/USD/i))

      const toSelect = screen.getByDisplayValue(/EUR/i)
      await user.click(toSelect)
      await user.click(screen.getByText(/USD/i))

      // Should show warning
      await waitFor(() => {
        expect(screen.getByText(/same/i)).toBeInTheDocument()
      })
    })
  })

  describe('Performance', () => {
    it('handles rapid currency changes', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)

      // Rapidly change amount
      for (let i = 1; i <= 5; i++) {
        await user.clear(amountInput)
        await user.type(amountInput, (i * 100).toString())
      }

      // Should handle all changes without errors
      await waitFor(() => {
        expect(screen.getByDisplayValue('500')).toBeInTheDocument()
      })
    })

    it('debounces conversion requests', async () => {
      render(<CurrencyConvert />)

      const amountInput = screen.getByLabelText(/Amount/i)

      // Type rapidly
      await user.clear(amountInput)
      await user.type(amountInput, '123')

      // Should debounce and not make excessive requests
      await waitFor(() => {
        expect(screen.getByDisplayValue('123')).toBeInTheDocument()
      })
    })
  })
})
