import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen, waitFor, mockFileDownload } from '../utils/test-utils'
import MatrixMath from '../../components/tools/matrix-math'
import userEvent from '@testing-library/user-event'

describe('Matrix Math Tool', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Basic Functionality', () => {
    it('renders the Matrix Math tool', () => {
      render(<MatrixMath />)
      expect(screen.getByText('Matrix Math & Linear Algebra Tool')).toBeInTheDocument()
    })

    it('performs matrix addition', async () => {
      render(<MatrixMath />)

      // Set up matrices for addition
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      const matrixBInput = screen.getByLabelText(/Matrix B/i)
      await user.clear(matrixBInput)
      await user.type(matrixBInput, '[[5,6],[7,8]]')

      // Select addition operation
      const operationSelect = screen.getByLabelText(/Operation/i)
      await user.click(operationSelect)
      await user.click(screen.getByText(/Addition/i))

      // Click calculate
      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show result
      await waitFor(() => {
        expect(screen.getByText(/Result Matrix/i)).toBeInTheDocument()
      })
    })

    it('performs matrix multiplication', async () => {
      render(<MatrixMath />)

      // Set up matrices for multiplication
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      const matrixBInput = screen.getByLabelText(/Matrix B/i)
      await user.clear(matrixBInput)
      await user.type(matrixBInput, '[[5,6],[7,8]]')

      // Select multiplication operation
      const operationSelect = screen.getByLabelText(/Operation/i)
      await user.click(operationSelect)
      await user.click(screen.getByText(/Multiplication/i))

      // Click calculate
      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show result
      await waitFor(() => {
        expect(screen.getByText(/Result Matrix/i)).toBeInTheDocument()
      })
    })

    it('calculates matrix determinant', async () => {
      render(<MatrixMath />)

      // Set up square matrix
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      // Select determinant operation
      const operationSelect = screen.getByLabelText(/Operation/i)
      await user.click(operationSelect)
      await user.click(screen.getByText(/Determinant/i))

      // Click calculate
      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show determinant result
      await waitFor(() => {
        expect(screen.getByText(/Determinant/i)).toBeInTheDocument()
      })
    })

    it('validates matrix dimensions', async () => {
      render(<MatrixMath />)

      // Set up matrices with incompatible dimensions
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2,3],[4,5,6]]') // 2x3

      const matrixBInput = screen.getByLabelText(/Matrix B/i)
      await user.clear(matrixBInput)
      await user.type(matrixBInput, '[[1,2],[3,4]]') // 2x2

      // Select multiplication operation
      const operationSelect = screen.getByLabelText(/Operation/i)
      await user.click(operationSelect)
      await user.click(screen.getByText(/Multiplication/i))

      // Click calculate
      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show dimension error
      await waitFor(() => {
        expect(screen.getByText(/incompatible/i)).toBeInTheDocument()
      })
    })
  })

  describe('Advanced Features', () => {
    it('shows matrix analysis', async () => {
      render(<MatrixMath />)

      // Set up matrix
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      // Click calculate to generate analysis
      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show matrix properties
      await waitFor(() => {
        expect(screen.getByText(/Matrix Properties/i)).toBeInTheDocument()
      })
    })

    it('applies matrix templates', async () => {
      render(<MatrixMath />)

      // Switch to templates tab
      await user.click(screen.getByRole('tab', { name: /Templates/i }))

      // Click on a template
      await user.click(screen.getByText(/Identity Matrix/i))

      // Should apply template
      await waitFor(() => {
        expect(screen.getByText(/Applied template/i)).toBeInTheDocument()
      })
    })

    it('manages operation history', async () => {
      render(<MatrixMath />)

      // Perform an operation
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Switch to history tab
      await user.click(screen.getByRole('tab', { name: /History/i }))

      await waitFor(() => {
        expect(screen.getByText(/operation/i)).toBeInTheDocument()
      })
    })

    it('exports matrix data', async () => {
      const { mockCreateObjectURL, mockClick } = mockFileDownload()

      render(<MatrixMath />)

      // Perform an operation
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[3,4]]')

      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Click export button
      const exportButton = screen.getByRole('button', { name: /JSON/i })
      await user.click(exportButton)

      expect(mockCreateObjectURL).toHaveBeenCalled()
      expect(mockClick).toHaveBeenCalled()
    })
  })

  describe('Matrix Builder', () => {
    it('creates matrices with custom dimensions', async () => {
      render(<MatrixMath />)

      // Switch to builder tab
      await user.click(screen.getByRole('tab', { name: /Builder/i }))

      // Set dimensions
      const rowsInput = screen.getByLabelText(/Rows/i)
      await user.clear(rowsInput)
      await user.type(rowsInput, '3')

      const colsInput = screen.getByLabelText(/Columns/i)
      await user.clear(colsInput)
      await user.type(colsInput, '3')

      // Generate matrix
      const generateButton = screen.getByRole('button', { name: /Generate/i })
      await user.click(generateButton)

      // Should create 3x3 matrix
      await waitFor(() => {
        expect(screen.getByText(/3×3/i)).toBeInTheDocument()
      })
    })

    it('generates identity matrices', async () => {
      render(<MatrixMath />)

      // Switch to builder tab
      await user.click(screen.getByRole('tab', { name: /Builder/i }))

      // Select identity matrix type
      const typeSelect = screen.getByLabelText(/Matrix Type/i)
      await user.click(typeSelect)
      await user.click(screen.getByText(/Identity/i))

      // Generate matrix
      const generateButton = screen.getByRole('button', { name: /Generate/i })
      await user.click(generateButton)

      // Should create identity matrix
      await waitFor(() => {
        expect(screen.getByText(/Identity/i)).toBeInTheDocument()
      })
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<MatrixMath />)

      expect(screen.getByLabelText(/Matrix A/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/Matrix B/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/Operation/i)).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      render(<MatrixMath />)

      const matrixInput = screen.getByLabelText(/Matrix A/i)
      matrixInput.focus()

      // Tab to next element
      await user.tab()
      expect(screen.getByLabelText(/Matrix B/i)).toHaveFocus()
    })

    it('has skip link for screen readers', () => {
      render(<MatrixMath />)

      const skipLink = screen.getByText(/Skip to main content/i)
      expect(skipLink).toBeInTheDocument()
      expect(skipLink).toHaveClass('sr-only')
    })
  })

  describe('Error Handling', () => {
    it('handles invalid matrix format', async () => {
      render(<MatrixMath />)

      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, 'invalid matrix')

      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show format error
      await waitFor(() => {
        expect(screen.getByText(/Invalid matrix format/i)).toBeInTheDocument()
      })
    })

    it('handles singular matrices', async () => {
      render(<MatrixMath />)

      // Set up singular matrix (determinant = 0)
      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, '[[1,2],[2,4]]')

      // Try to calculate inverse
      const operationSelect = screen.getByLabelText(/Operation/i)
      await user.click(operationSelect)
      await user.click(screen.getByText(/Inverse/i))

      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      // Should show singular matrix error
      await waitFor(() => {
        expect(screen.getByText(/singular/i)).toBeInTheDocument()
      })
    })
  })

  describe('Performance', () => {
    it('handles large matrices efficiently', async () => {
      render(<MatrixMath />)

      // Create large matrix
      const largeMatrix = Array(10)
        .fill(0)
        .map(() => Array(10).fill(1))
      const matrixString = JSON.stringify(largeMatrix)

      const matrixAInput = screen.getByLabelText(/Matrix A/i)
      await user.clear(matrixAInput)
      await user.type(matrixAInput, matrixString)

      const startTime = performance.now()

      const calculateButton = screen.getByRole('button', { name: /Calculate/i })
      await user.click(calculateButton)

      await waitFor(() => {
        expect(screen.getByText(/Result/i)).toBeInTheDocument()
      })

      const endTime = performance.now()
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
    })
  })
})
