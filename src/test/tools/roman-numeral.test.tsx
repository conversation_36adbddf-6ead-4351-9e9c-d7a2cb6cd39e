import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, mockFileDownload, mockClipboard } from '../utils/test-utils'
import RomanNumeral from '../../components/tools/roman-numeral'
import userEvent from '@testing-library/user-event'

describe('Roman Numeral Tool', () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
  })

  describe('Basic Functionality', () => {
    it('renders the Roman Numeral tool', () => {
      render(<RomanNumeral />)
      expect(screen.getByText('Roman Numeral & Classical Number System Tool')).toBeInTheDocument()
    })

    it('converts Arabic numbers to Roman numerals', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '1984')

      await waitFor(() => {
        const romanInput = screen.getByLabelText(/Roman Numeral/i) as HTMLInputElement
        expect(romanInput.value).toBe('MCMLXXXIV')
      })
    })

    it('converts Roman numerals to Arabic numbers', async () => {
      render(<RomanNumeral />)

      const romanInput = screen.getByLabelText(/Roman Numeral/i)
      await user.clear(romanInput)
      await user.type(romanInput, 'MCMLXXXIV')

      await waitFor(() => {
        const arabicInput = screen.getByLabelText(/Arabic Number/i) as HTMLInputElement
        expect(arabicInput.value).toBe('1984')
      })
    })

    it('handles invalid Roman numerals', async () => {
      render(<RomanNumeral />)

      const romanInput = screen.getByLabelText(/Roman Numeral/i)
      await user.clear(romanInput)
      await user.type(romanInput, 'INVALID')

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/Invalid Roman numeral format/i)).toBeInTheDocument()
      })
    })

    it('handles out-of-range numbers', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '5000')

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/Number must be less than 4000/i)).toBeInTheDocument()
      })
    })
  })

  describe('Advanced Features', () => {
    it('shows detailed analysis', async () => {
      render(<RomanNumeral />)

      // Convert a number first
      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '1984')

      // Switch to analysis tab
      await user.click(screen.getByRole('tab', { name: /Analysis/i }))

      await waitFor(() => {
        expect(screen.getByText(/Historical Context/i)).toBeInTheDocument()
        expect(screen.getByText(/Mathematical Properties/i)).toBeInTheDocument()
      })
    })

    it('applies templates correctly', async () => {
      render(<RomanNumeral />)

      // Switch to templates tab
      await user.click(screen.getByRole('tab', { name: /Templates/i }))

      // Click on a template
      await user.click(screen.getByText(/Basic Numbers/i))

      // Should switch back to converter tab and apply template
      await waitFor(() => {
        const arabicInput = screen.getByLabelText(/Arabic Number/i) as HTMLInputElement
        expect(parseInt(arabicInput.value)).toBeGreaterThan(0)
        expect(parseInt(arabicInput.value)).toBeLessThanOrEqual(10)
      })
    })

    it('manages conversion history', async () => {
      render(<RomanNumeral />)

      // Perform a conversion
      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '42')

      // Switch to history tab
      await user.click(screen.getByRole('tab', { name: /History/i }))

      await waitFor(() => {
        expect(screen.getByText(/42 = XLII/i)).toBeInTheDocument()
      })
    })

    it('exports conversion data', async () => {
      const { mockCreateObjectURL, mockClick } = mockFileDownload()

      render(<RomanNumeral />)

      // Perform a conversion
      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '42')

      // Click export button
      const exportButton = screen.getByRole('button', { name: /JSON/i })
      await user.click(exportButton)

      expect(mockCreateObjectURL).toHaveBeenCalled()
      expect(mockClick).toHaveBeenCalled()
    })

    it('copies to clipboard', async () => {
      const { mockWriteText } = mockClipboard()

      render(<RomanNumeral />)

      // Perform a conversion
      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '42')

      // Click copy button
      const copyButton = screen.getByRole('button', { name: /copy/i })
      await user.click(copyButton)

      expect(mockWriteText).toHaveBeenCalledWith('42 = XLII')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<RomanNumeral />)

      expect(screen.getByLabelText(/Arabic Number/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/Roman Numeral/i)).toBeInTheDocument()
    })

    it('supports keyboard navigation', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      arabicInput.focus()

      // Tab to next element
      await user.tab()
      expect(screen.getByRole('button', { name: /swap/i })).toHaveFocus()
    })

    it('has skip link for screen readers', () => {
      render(<RomanNumeral />)

      const skipLink = screen.getByText(/Skip to main content/i)
      expect(skipLink).toBeInTheDocument()
      expect(skipLink).toHaveClass('sr-only')
    })
  })

  describe('Error Handling', () => {
    it('handles component errors gracefully', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      // Force an error by mocking a failing component
      const ErrorComponent = () => {
        throw new Error('Test error')
      }

      render(<ErrorComponent />)

      // Should show error boundary
      expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument()

      consoleSpy.mockRestore()
    })

    it('validates input ranges', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)
      await user.clear(arabicInput)
      await user.type(arabicInput, '0')

      await waitFor(() => {
        expect(screen.getByText(/Number must be positive/i)).toBeInTheDocument()
      })
    })
  })

  describe('Performance', () => {
    it('converts numbers efficiently', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)

      const startTime = performance.now()
      await user.clear(arabicInput)
      await user.type(arabicInput, '3999')

      await waitFor(() => {
        const romanInput = screen.getByLabelText(/Roman Numeral/i) as HTMLInputElement
        expect(romanInput.value).toBe('MMMCMXCIX')
      })

      const endTime = performance.now()
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })

    it('handles rapid input changes', async () => {
      render(<RomanNumeral />)

      const arabicInput = screen.getByLabelText(/Arabic Number/i)

      // Rapidly change input
      for (let i = 1; i <= 10; i++) {
        await user.clear(arabicInput)
        await user.type(arabicInput, i.toString())
      }

      // Should handle all changes without errors
      await waitFor(() => {
        const romanInput = screen.getByLabelText(/Roman Numeral/i) as HTMLInputElement
        expect(romanInput.value).toBe('X')
      })
    })
  })
})
