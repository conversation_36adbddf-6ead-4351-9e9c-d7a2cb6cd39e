// src/types/common.ts
export interface BaseFile {
  id: string
  file: File
  name: string
  size: number
  type: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
  timestamp: number
  processingTime?: number
}

export interface BaseStats {
  totalFiles: number
  processingTime: number
  averageProcessingTime: number
}

export interface HistoryEntryBase {
  id: string
  timestamp: number
  description: string
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'txt' | 'html'
  includeMetadata: boolean
  prettyPrint?: boolean
}