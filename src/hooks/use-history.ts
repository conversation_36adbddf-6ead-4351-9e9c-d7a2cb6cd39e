// src/hooks/use-history.ts
import { useState, useCallback } from 'react'

export interface HistoryEntry {
  id: string
  timestamp: number
  description: string
  [key: string]: any
}

export const useHistory = <T extends HistoryEntry>(maxEntries = 10) => {
  const [history, setHistory] = useState<T[]>([])

  const addToHistory = useCallback((entry: T) => {
    setHistory(prev => [entry, ...prev.slice(0, maxEntries - 1)])
  }, [maxEntries])

  const clearHistory = useCallback(() => {
    setHistory([])
  }, [])

  return { history, addToHistory, clearHistory }
}