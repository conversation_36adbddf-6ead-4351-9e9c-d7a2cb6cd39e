// src/hooks/use-keyboard-shortcuts.ts
import { useEffect } from 'react'

type ShortcutHandler = (e: KeyboardEvent) => void
type ShortcutMap = Record<string, ShortcutHandler>

export const useKeyboardShortcuts = (shortcuts: ShortcutMap) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 创建快捷键标识符，例如 "ctrl+s"
      const key = e.key.toLowerCase()
      const ctrlKey = e.ctrlKey || e.metaKey
      const shortcutKey = ctrlKey ? `ctrl+${key}` : key

      if (shortcuts[shortcutKey]) {
        shortcuts[shortcutKey](e)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts])
}