// src/hooks/use-drag-drop.ts
import { useState, useRef, useCallback } from 'react'
import { toast } from 'sonner'

export const useDragAndDrop = (
  onFilesDropped: (files: File[]) => void,
  fileTypes?: string[] | string
) => {
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)

      let files = Array.from(e.dataTransfer.files)
      
      // 如果指定了文件类型，进行过滤
      if (fileTypes) {
        const types = Array.isArray(fileTypes) ? fileTypes : [fileTypes]
        files = files.filter(file => {
          if (types.includes('*')) return true
          return types.some(type => {
            if (type.endsWith('/*')) {
              const mainType = type.split('/')[0]
              return file.type.startsWith(mainType + '/')
            }
            return file.type === type
          })
        })
      }

      if (files.length > 0) {
        onFilesDropped(files)
      } else {
        toast.error('Please drop valid files')
      }
    },
    [onFilesDropped, fileTypes]
  )

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        const files = Array.from(e.target.files)
        onFilesDropped(files)
      }
    },
    [onFilesDropped]
  )

  return { dragActive, fileInputRef, handleDrag, handleDrop, handleFileInput }
}