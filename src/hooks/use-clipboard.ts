// src/hooks/use-clipboard.ts
import { useState, useCallback } from 'react'
import { toast } from 'sonner'

export const useCopyToClipboard = (resetDelay = 2000) => {
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const copyToClipboard = useCallback(async (text: string, label?: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(label || 'text')
      toast.success(`${label || 'Text'} copied to clipboard`)

      // Reset copied state after delay
      setTimeout(() => setCopiedText(null), resetDelay)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }, [resetDelay])

  return { copyToClipboard, copiedText }
}