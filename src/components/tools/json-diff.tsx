import { useState } from 'react'
/**
 * JsonDiff - JSON Diff Viewer
 * @returns 组件
 */
// 输入两个 JSON，显示差异（仅对比键）
const JsonDiff = () => {
  const [a, setA] = useState('')
  const [b, setB] = useState('')
  let diff = ''
  try {
    const o1 = JSON.parse(a),
      o2 = JSON.parse(b)
    const keys1 = Object.keys(o1),
      keys2 = Object.keys(o2)
    const only1 = keys1.filter((k) => !keys2.includes(k))
    const only2 = keys2.filter((k) => !keys1.includes(k))
    diff = `仅A有: ${only1.join(', ')}\n仅B有: ${only2.join(', ')}`
  } catch {
    diff = '无效 JSON'
  }
  return (
    <div className="flex flex-col gap-4">
      <textarea
        className="w-full min-h-[60px] rounded border p-2"
        value={a}
        onChange={(e) => setA(e.target.value)}
        placeholder="JSON A..."
      />
      <textarea
        className="w-full min-h-[60px] rounded border p-2"
        value={b}
        onChange={(e) => setB(e.target.value)}
        placeholder="JSON B..."
      />
      <div className="bg-muted p-2 rounded text-xs font-mono whitespace-pre-line">{diff}</div>
    </div>
  )
}
export default JsonDiff
