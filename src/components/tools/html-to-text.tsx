import { useState, useRef } from 'react'
import { Textarea } from '../ui/textarea'
import { Button } from '../ui/button'
import { Label } from '../ui/label'

/**
 * HtmlToText - HTML 提取纯文本
 * @returns 组件
 */
const HtmlToText = () => {
  const [html, setHtml] = useState('')
  const [text, setText] = useState('')
  const [error, setError] = useState('')
  const textAreaRef = useRef<HTMLTextAreaElement>(null)

  // 自动提取纯文本
  const extract = (input: string) => {
    try {
      setError('')
      const el = document.createElement('div')
      el.innerHTML = input
      setText(el.textContent || '')
    } catch {
      setError('HTML 解析失败，请检查输入内容。')
      setText('')
    }
  }

  // 输入变化时自动提取
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setHtml(value)
    extract(value)
  }

  // 粘贴时自动处理
  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const value = e.clipboardData.getData('text')
    setHtml(value)
    extract(value)
    e.preventDefault()
  }

  // 复制输出
  const handleCopy = () => {
    if (textAreaRef.current) {
      textAreaRef.current.select()
      document.execCommand('copy')
    }
  }

  // 清空输入输出
  const handleClear = () => {
    setHtml('')
    setText('')
    setError('')
  }

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* 左侧：HTML 输入 */}
      <div className="flex-1 flex flex-col gap-4">
        <Label className="font-bold">HTML 输入</Label>
        <Textarea
          className="w-full min-h-[180px] rounded border p-2 bg-background text-foreground"
          placeholder="输入 HTML... (Type or paste HTML here...)"
          value={html}
          onChange={handleChange}
          onPaste={handlePaste}
        />
        <div className="flex gap-2">
          <Button className="btn" onClick={handleClear} type="button">
            清空/Clear
          </Button>
        </div>
      </div>
      {/* 右侧：纯文本输出及操作 */}
      <div className="flex-1 flex flex-col gap-4">
        <Label className="font-bold">纯文本输出</Label>
        <Textarea
          ref={textAreaRef}
          className="w-full min-h-[180px] rounded border p-2 bg-muted text-foreground"
          value={text}
          readOnly
        />
        <div className="flex gap-2">
          <Button className="btn" onClick={handleCopy} type="button">
            复制/Copy
          </Button>
        </div>
        {error && <div className="text-red-500">{error}</div>}
      </div>
    </div>
  )
}

export default HtmlToText
