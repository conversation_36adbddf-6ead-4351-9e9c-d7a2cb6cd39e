import { useState } from 'react'
import { Input } from '@/components/ui/input'

/**
 * BaseN - 进制转换
 * @returns 组件
 */
// 输入数字和进制，互转
const BaseN = () => {
  const [num, setNum] = useState('10')
  const [from, setFrom] = useState(10)
  const [to, setTo] = useState(2)
  let result = ''
  try {
    result = parseInt(num, from).toString(to)
  } catch {
    result = '格式错误'
  }
  return (
    <div className="flex flex-col gap-4">
      <Input
        value={num}
        onChange={(e) => setNum(e.target.value)}
        className="w-32 border rounded px-2"
        color="primary"
      />
      <Input
        type="number"
        value={from}
        onChange={(e) => setFrom(Number(e.target.value))}
        className="w-20 border rounded px-2"
        color="primary"
      />
      <Input
        type="number"
        value={to}
        onChange={(e) => setTo(Number(e.target.value))}
        className="w-20 border rounded px-2"
        color="primary"
      />
      <div className="text-sm text-muted-foreground">结果/Result: {result}</div>
    </div>
  )
}

export default BaseN
