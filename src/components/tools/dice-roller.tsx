import { useState } from 'react'
/**
 * DiceRoller - RPG 骰子
 * @returns 组件
 */
// 输入面数和数量，掷骰子
const DiceRoller = () => {
  const [sides, setSides] = useState(6)
  const [count, setCount] = useState(1)
  const [result, setResult] = useState<number[]>([])
  const roll = () => setResult(Array.from({ length: count }, () => Math.floor(Math.random() * sides) + 1))
  return (
    <div className="flex flex-col gap-4 items-center">
      <div className="flex gap-2">
        <input
          type="number"
          value={sides}
          onChange={(e) => setSides(Number(e.target.value))}
          className="w-20 border rounded px-2"
        />
        <input
          type="number"
          value={count}
          onChange={(e) => setCount(Number(e.target.value))}
          className="w-20 border rounded px-2"
        />
      </div>
      <button className="btn" onClick={roll}>
        掷骰/Roll
      </button>
      <div className="flex gap-2 font-mono text-lg select-all">
        {result.map((n, i) => (
          <span key={i}>{n}</span>
        ))}
      </div>
    </div>
  )
}
export default DiceRoller
