import { useState } from 'react'

/**
 * QuadraticSolver - 解一元二次方程
 * @returns 组件
 */
// 输入 a, b, c，解 ax^2+bx+c=0
const QuadraticSolver = () => {
  const [a, setA] = useState(1)
  const [b, setB] = useState(0)
  const [c, setC] = useState(0)
  let result = ''
  const delta = b * b - 4 * a * c
  if (delta > 0) {
    const x1 = ((-b + Math.sqrt(delta)) / (2 * a)).toFixed(2)
    const x2 = ((-b - Math.sqrt(delta)) / (2 * a)).toFixed(2)
    result = `x1=${x1}, x2=${x2}`
  } else if (delta === 0) {
    result = `x=${(-b / (2 * a)).toFixed(2)}`
  } else {
    result = '无实数解/No real root'
  }
  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 items-center">
        <input
          type="number"
          value={a}
          onChange={(e) => setA(Number(e.target.value))}
          className="w-16 border rounded px-2"
        />
        <input
          type="number"
          value={b}
          onChange={(e) => setB(Number(e.target.value))}
          className="w-16 border rounded px-2"
        />
        <input
          type="number"
          value={c}
          onChange={(e) => setC(Number(e.target.value))}
          className="w-16 border rounded px-2"
        />
      </div>
      <div className="text-sm text-muted-foreground">结果/Result: {result}</div>
    </div>
  )
}

export default QuadraticSolver
