import { useEffect, useRef, useState } from 'react'

/**
 * CountdownTimer - 倒计时
 * @returns 组件
 */
// 输入秒数，倒计时显示
const CountdownTimer = () => {
  const [sec, setSec] = useState(60)
  const [left, setLeft] = useState(60)
  const [running, setRunning] = useState(false)
  const timer = useRef<NodeJS.Timeout | null>(null)
  useEffect(() => {
    if (!running) return
    timer.current = setInterval(() => {
      setLeft((l) => {
        if (l <= 1) {
          setRunning(false)
          if (timer.current) {
            clearInterval(timer.current)
          }
          return 0
        }
        return l - 1
      })
    }, 1000)
    return () => {
      if (timer.current) {
        clearInterval(timer.current)
      }
    }
  }, [running])
  const start = () => {
    setLeft(sec)
    setRunning(true)
  }
  return (
    <div className="flex flex-col gap-4 items-center">
      <input
        type="number"
        value={sec}
        onChange={(e) => setSec(Number(e.target.value))}
        className="w-24 border rounded px-2"
      />
      <button className="btn" onClick={start} disabled={running}>
        开始/Start
      </button>
      <div className="text-2xl font-mono">{left}s</div>
    </div>
  )
}

export default CountdownTimer
