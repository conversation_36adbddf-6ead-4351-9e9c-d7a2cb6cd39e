import React, { useState } from 'react'
/**
 * JSON 可视化工具
 * JSON visualizer
 * 允许用户输入 JSON 并以树形结构展示，纯前端实现。
 */

function renderTree(data: any, level = 0): React.ReactNode {
  if (typeof data === 'object' && data !== null) {
    return (
      <ul className="pl-4 border-l border-zinc-300 dark:border-zinc-700">
        {Object.entries(data).map(([key, value]) => (
          <li key={key + level} className="mb-1">
            <span className="font-mono text-blue-600 dark:text-blue-400">{key}:</span>{' '}
            {typeof value === 'object' ? renderTree(value, level + 1) : String(value)}
          </li>
        ))}
      </ul>
    )
  }
  return <span className="text-green-700 dark:text-green-400">{String(data)}</span>
}

const JsonPlot: React.FC = () => {
  const [input, setInput] = useState('')
  const [json, setJson] = useState<any>(null)
  const [error, setError] = useState('')

  /**
   * 解析并可视化 JSON
   * Parse and visualize JSON
   */
  const handleParse = () => {
    try {
      setJson(JSON.parse(input))
      setError('')
    } catch (e) {
      setError('JSON 解析错误 / Invalid JSON')
      setJson(null)
    }
  }

  return (
    <div className="space-y-4">
      <textarea
        className="w-full h-32 p-2 border rounded dark:bg-zinc-900 dark:text-white"
        placeholder="请输入 JSON / Enter JSON"
        value={input}
        onChange={(e) => setInput(e.target.value)}
      />
      <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onClick={handleParse}>
        可视化 / Visualize
      </button>
      {error && <div className="text-red-600 dark:text-red-400">{error}</div>}
      {json && <div className="overflow-x-auto">{renderTree(json)}</div>}
    </div>
  )
}

export default JsonPlot
