import { nanoid } from 'nanoid'
import { useState } from 'react'

const UuidBatch = () => {
  const [count, setCount] = useState(5)
  const [list, setList] = useState<string[]>([])
  const gen = () => setList(Array.from({ length: count }, () => nanoid(16)))
  return (
    <div className="flex flex-col gap-4 items-center">
      <input
        type="number"
        value={count}
        onChange={(e) => setCount(Number(e.target.value))}
        className="w-20 border rounded px-2"
      />
      <button className="btn" onClick={gen}>
        生成/Generate
      </button>
      <div className="flex flex-col gap-1 w-full">
        {list.map((u, i) => (
          <div key={i} className="font-mono text-xs select-all">
            {u}
          </div>
        ))}
      </div>
    </div>
  )
}
export default UuidBatch
