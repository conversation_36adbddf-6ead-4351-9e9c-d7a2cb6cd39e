import React, { useCallback, useState, useMemo, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import {
  Download,
  Upload,
  Trash2,
  Copy,
  Check,
  Shuffle,
  RotateCcw,
  Zap,
  Settings,
  CheckCircle2,
  AlertCircle,
  FileText,
  BookOpen,
  Search,
  ArrowRight,
  Eye,
  EyeOff,
  Globe,
  Shield,
  Server,
  Clock,
  MapPin,
  Wifi,
  Database,
  Info,
  RefreshCw,
  ExternalLink,
  Network,
  Monitor,
  Router,
  Smartphone,
  Building,
  Flag,
  Navigation,
  Activity,
  Link,
  Hash,
  Key,
  Lock,
  Unlock,
  File,
  Files,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Combine,
  Split,
  RotateClockwise,
  RotateCounterClockwise
} from 'lucide-react'
import { PDFDocument, PDFPage, degrees } from 'pdf-lib'

// Enhanced Types
interface PDFFile {
  id: string
  file: File
  name: string
  size: number
  pageCount?: number
  isValid: boolean
  error?: string
  thumbnail?: string
  metadata?: PDFMetadata
  pages?: PDFPageInfo[]
  createdAt: Date
}

interface PDFPageInfo {
  pageNumber: number
  width: number
  height: number
  rotation: number
  selected: boolean
  thumbnail?: string
}

interface PDFMetadata {
  title?: string
  author?: string
  subject?: string
  creator?: string
  producer?: string
  creationDate?: Date
  modificationDate?: Date
  keywords?: string[]
  pageCount: number
  fileSize: number
  version?: string
  encrypted: boolean
  permissions?: PDFPermissions
}

interface PDFPermissions {
  canPrint: boolean
  canModify: boolean
  canCopy: boolean
  canAnnotate: boolean
  canFillForms: boolean
  canExtractForAccessibility: boolean
  canAssemble: boolean
  canPrintHighQuality: boolean
}

interface MergeOperation {
  id: string
  files: PDFFile[]
  settings: MergeSettings
  result?: MergeResult
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  error?: string
  createdAt: Date
  completedAt?: Date
}

interface MergeResult {
  fileName: string
  fileSize: number
  pageCount: number
  processingTime: number
  downloadUrl?: string
  statistics: MergeStatistics
}

interface MergeStatistics {
  totalFiles: number
  totalPages: number
  totalSize: number
  compressionRatio: number
  processingTime: number
  qualityScore: number
  optimizationSavings: number
}

interface MergeSettings {
  outputFileName: string
  pageOrder: 'original' | 'reverse' | 'custom'
  includeBookmarks: boolean
  includeMetadata: boolean
  optimizeSize: boolean
  removeBlankPages: boolean
  pageRange?: PageRange[]
  watermark?: WatermarkSettings
  security?: SecuritySettings
  quality: 'high' | 'medium' | 'low'
  compression: boolean
}

interface PageRange {
  fileId: string
  startPage: number
  endPage: number
}

interface WatermarkSettings {
  enabled: boolean
  text: string
  opacity: number
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  fontSize: number
  color: string
}

interface SecuritySettings {
  enabled: boolean
  userPassword?: string
  ownerPassword?: string
  permissions: PDFPermissions
}

interface ProcessingBatch {
  id: string
  operations: MergeOperation[]
  count: number
  settings: ProcessingSettings
  createdAt: Date
  statistics: BatchStatistics
}

interface BatchStatistics {
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  totalFilesProcessed: number
  totalPagesProcessed: number
  totalSizeProcessed: number
  averageProcessingTime: number
  successRate: number
}

interface ProcessingSettings {
  maxFileSize: number
  maxFiles: number
  allowedFormats: string[]
  autoOptimize: boolean
  preserveQuality: boolean
  enableParallelProcessing: boolean
  exportFormat: ExportFormat
  realTimePreview: boolean
}

interface PDFTemplate {
  id: string
  name: string
  description: string
  category: string
  settings: Partial<MergeSettings>
  useCase: string[]
  examples: string[]
}

interface PDFValidation {
  isValid: boolean
  errors: PDFError[]
  warnings: string[]
  suggestions: string[]
}

interface PDFError {
  message: string
  type: 'format' | 'size' | 'corruption' | 'security' | 'compatibility'
  severity: 'error' | 'warning' | 'info'
}

// Enums
type ExportFormat = 'pdf' | 'zip'

// Utility functions
const generateId = (): string => Math.random().toString(36).substring(2, 11)

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// PDF processing functions
const processPDFFile = async (file: File): Promise<PDFFile> => {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const pdfDoc = await PDFDocument.load(arrayBuffer)

    // Extract metadata
    const pageCount = pdfDoc.getPageCount()
    const title = pdfDoc.getTitle()
    const author = pdfDoc.getAuthor()
    const subject = pdfDoc.getSubject()
    const creator = pdfDoc.getCreator()
    const producer = pdfDoc.getProducer()
    const creationDate = pdfDoc.getCreationDate()
    const modificationDate = pdfDoc.getModificationDate()
    const keywords = pdfDoc.getKeywords()?.split(',').map(k => k.trim()) || []

    // Extract page information
    const pages: PDFPageInfo[] = []
    for (let i = 0; i < pageCount; i++) {
      const page = pdfDoc.getPage(i)
      const { width, height } = page.getSize()
      const rotation = page.getRotation().angle

      pages.push({
        pageNumber: i + 1,
        width,
        height,
        rotation,
        selected: true // Default to selected
      })
    }

    const metadata: PDFMetadata = {
      title: title || undefined,
      author: author || undefined,
      subject: subject || undefined,
      creator: creator || undefined,
      producer: producer || undefined,
      creationDate: creationDate || undefined,
      modificationDate: modificationDate || undefined,
      keywords,
      pageCount,
      fileSize: file.size,
      encrypted: false, // pdf-lib doesn't provide direct access to encryption status
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true,
        canFillForms: true,
        canExtractForAccessibility: true,
        canAssemble: true,
        canPrintHighQuality: true
      }
    }

    return {
      id: generateId(),
      file,
      name: file.name,
      size: file.size,
      pageCount,
      isValid: true,
      metadata,
      pages,
      createdAt: new Date()
    }
  } catch (error) {
    return {
      id: generateId(),
      file,
      name: file.name,
      size: file.size,
      isValid: false,
      error: error instanceof Error ? error.message : 'Failed to process PDF',
      createdAt: new Date()
    }
  }
}

const mergePDFs = async (
  pdfFiles: PDFFile[],
  settings: MergeSettings,
  onProgress?: (progress: number) => void
): Promise<MergeResult> => {
  const startTime = performance.now()

  try {
    const mergedPdf = await PDFDocument.create()
    let totalPages = 0
    let processedPages = 0

    // Calculate total pages for progress tracking
    pdfFiles.forEach(pdfFile => {
      if (pdfFile.isValid && pdfFile.pages) {
        totalPages += pdfFile.pages.filter(page => page.selected).length
      }
    })

    // Set metadata for merged PDF
    if (settings.includeMetadata) {
      mergedPdf.setTitle(settings.outputFileName.replace('.pdf', ''))
      mergedPdf.setCreator('PDF Merge Tool')
      mergedPdf.setProducer('Enhanced PDF Merger')
      mergedPdf.setCreationDate(new Date())
    }

    // Process files in order
    for (const pdfFile of pdfFiles) {
      if (!pdfFile.isValid || !pdfFile.pages) continue

      const arrayBuffer = await pdfFile.file.arrayBuffer()
      const sourcePdf = await PDFDocument.load(arrayBuffer)

      // Get selected pages
      const selectedPageIndices = pdfFile.pages
        .filter(page => page.selected)
        .map(page => page.pageNumber - 1)

      if (selectedPageIndices.length === 0) continue

      // Copy selected pages
      const copiedPages = await mergedPdf.copyPages(sourcePdf, selectedPageIndices)

      copiedPages.forEach(page => {
        // Apply rotation if needed
        const originalPageInfo = pdfFile.pages!.find(p => p.pageNumber === selectedPageIndices[copiedPages.indexOf(page)] + 1)
        if (originalPageInfo && originalPageInfo.rotation !== 0) {
          page.setRotation(degrees(originalPageInfo.rotation))
        }

        mergedPdf.addPage(page)
        processedPages++

        // Update progress
        if (onProgress) {
          onProgress((processedPages / totalPages) * 100)
        }
      })
    }

    // Apply watermark if enabled
    if (settings.watermark?.enabled && settings.watermark.text) {
      const pages = mergedPdf.getPages()
      pages.forEach(page => {
        const { width, height } = page.getSize()
        let x = width / 2
        let y = height / 2

        // Adjust position based on settings
        switch (settings.watermark!.position) {
          case 'top-left':
            x = 50
            y = height - 50
            break
          case 'top-right':
            x = width - 50
            y = height - 50
            break
          case 'bottom-left':
            x = 50
            y = 50
            break
          case 'bottom-right':
            x = width - 50
            y = 50
            break
        }

        page.drawText(settings.watermark!.text, {
          x,
          y,
          size: settings.watermark!.fontSize,
          opacity: settings.watermark!.opacity,
        })
      })
    }

    // Save with optimization if enabled
    const pdfBytes = await mergedPdf.save({
      useObjectStreams: settings.optimizeSize,
      addDefaultPage: false
    })

    const endTime = performance.now()
    const processingTime = endTime - startTime

    // Calculate statistics
    const totalSize = pdfFiles.reduce((sum, file) => sum + file.size, 0)
    const compressionRatio = totalSize > 0 ? (totalSize - pdfBytes.length) / totalSize : 0

    return {
      fileName: settings.outputFileName,
      fileSize: pdfBytes.length,
      pageCount: mergedPdf.getPageCount(),
      processingTime,
      statistics: {
        totalFiles: pdfFiles.length,
        totalPages: totalPages,
        totalSize: totalSize,
        compressionRatio,
        processingTime,
        qualityScore: calculateQualityScore(pdfFiles, settings),
        optimizationSavings: totalSize - pdfBytes.length
      }
    }
  } catch (error) {
    throw new Error(`PDF merge failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

const calculateQualityScore = (pdfFiles: PDFFile[], settings: MergeSettings): number => {
  let score = 100

  // Deduct points for potential quality issues
  if (!settings.includeMetadata) score -= 5
  if (!settings.includeBookmarks) score -= 5
  if (settings.optimizeSize) score -= 10 // Optimization might reduce quality
  if (settings.compression) score -= 15

  // Add points for good practices
  if (settings.removeBlankPages) score += 5
  if (settings.watermark?.enabled) score += 5

  return Math.max(0, Math.min(100, score))
}

// PDF templates
const pdfTemplates: PDFTemplate[] = [
  {
    id: 'basic-merge',
    name: 'Basic Merge',
    description: 'Simple PDF merging with default settings',
    category: 'Basic',
    settings: {
      outputFileName: 'merged-document.pdf',
      pageOrder: 'original',
      includeBookmarks: true,
      includeMetadata: true,
      optimizeSize: false,
      removeBlankPages: false,
      quality: 'high',
      compression: false
    },
    useCase: ['Document combination', 'Report compilation', 'File consolidation'],
    examples: ['Merge multiple reports', 'Combine contract pages', 'Consolidate presentations']
  },
  {
    id: 'optimized-merge',
    name: 'Optimized Merge',
    description: 'PDF merging with size optimization',
    category: 'Optimization',
    settings: {
      outputFileName: 'optimized-merged.pdf',
      pageOrder: 'original',
      includeBookmarks: true,
      includeMetadata: true,
      optimizeSize: true,
      removeBlankPages: true,
      quality: 'medium',
      compression: true
    },
    useCase: ['File size reduction', 'Email attachments', 'Web distribution'],
    examples: ['Compress large documents', 'Optimize for email', 'Reduce storage space']
  },
  {
    id: 'presentation-merge',
    name: 'Presentation Merge',
    description: 'Merge presentation slides with high quality',
    category: 'Presentation',
    settings: {
      outputFileName: 'presentation-merged.pdf',
      pageOrder: 'original',
      includeBookmarks: false,
      includeMetadata: true,
      optimizeSize: false,
      removeBlankPages: true,
      quality: 'high',
      compression: false
    },
    useCase: ['Slide compilation', 'Training materials', 'Conference presentations'],
    examples: ['Combine slide decks', 'Merge training modules', 'Consolidate presentations']
  },
  {
    id: 'document-archive',
    name: 'Document Archive',
    description: 'Archive multiple documents with metadata preservation',
    category: 'Archive',
    settings: {
      outputFileName: 'document-archive.pdf',
      pageOrder: 'original',
      includeBookmarks: true,
      includeMetadata: true,
      optimizeSize: true,
      removeBlankPages: false,
      quality: 'medium',
      compression: true
    },
    useCase: ['Document archiving', 'Record keeping', 'Digital storage'],
    examples: ['Archive contracts', 'Store invoices', 'Preserve documents']
  },
  {
    id: 'watermarked-merge',
    name: 'Watermarked Merge',
    description: 'Merge PDFs with watermark protection',
    category: 'Security',
    settings: {
      outputFileName: 'watermarked-merged.pdf',
      pageOrder: 'original',
      includeBookmarks: true,
      includeMetadata: true,
      optimizeSize: false,
      removeBlankPages: false,
      quality: 'high',
      compression: false,
      watermark: {
        enabled: true,
        text: 'CONFIDENTIAL',
        opacity: 0.3,
        position: 'center',
        fontSize: 48,
        color: '#FF0000'
      }
    },
    useCase: ['Confidential documents', 'Draft protection', 'Copyright marking'],
    examples: ['Mark confidential files', 'Protect drafts', 'Add copyright notice']
  },
  {
    id: 'custom-order',
    name: 'Custom Order Merge',
    description: 'Merge PDFs with custom page ordering',
    category: 'Custom',
    settings: {
      outputFileName: 'custom-order-merged.pdf',
      pageOrder: 'custom',
      includeBookmarks: true,
      includeMetadata: true,
      optimizeSize: false,
      removeBlankPages: false,
      quality: 'high',
      compression: false
    },
    useCase: ['Custom arrangement', 'Specific ordering', 'Manual organization'],
    examples: ['Reorder chapters', 'Custom page sequence', 'Manual arrangement']
  }
]

// Validation functions
const validatePDFFile = (file: File): PDFValidation => {
  const validation: PDFValidation = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  }

  // File type validation
  if (file.type !== 'application/pdf') {
    validation.isValid = false
    validation.errors.push({
      message: 'File is not a PDF',
      type: 'format',
      severity: 'error'
    })
  }

  // File size validation (100MB limit)
  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    validation.isValid = false
    validation.errors.push({
      message: `File size exceeds ${formatFileSize(maxSize)} limit`,
      type: 'size',
      severity: 'error'
    })
  }

  // File size warnings
  if (file.size > 50 * 1024 * 1024) {
    validation.warnings.push('Large file size may affect processing performance')
  }

  if (file.size < 1024) {
    validation.warnings.push('File size is very small, may be corrupted')
  }

  // File name validation
  if (file.name.length > 255) {
    validation.warnings.push('File name is very long')
    validation.suggestions.push('Consider shortening the file name')
  }

  if (!/^[\w\-. ]+\.pdf$/i.test(file.name)) {
    validation.warnings.push('File name contains special characters')
    validation.suggestions.push('Use only letters, numbers, spaces, hyphens, and dots')
  }

  return validation
}

// Error boundary component
class MergePDFErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Merge PDF error:', error, errorInfo)
    toast.error('An unexpected error occurred during PDF processing')
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="text-red-600">
                <h3 className="font-semibold">Something went wrong</h3>
                <p className="text-sm">Please refresh the page and try again.</p>
              </div>
              <Button onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Custom hooks
const usePDFMerger = () => {
  const [operations, setOperations] = useState<MergeOperation[]>([])

  const createMergeOperation = useCallback((files: PDFFile[], settings: MergeSettings): MergeOperation => {
    const operation: MergeOperation = {
      id: generateId(),
      files,
      settings,
      status: 'pending',
      progress: 0,
      createdAt: new Date()
    }

    setOperations(prev => [operation, ...prev])
    return operation
  }, [])

  const processMergeOperation = useCallback(async (operationId: string) => {
    setOperations(prev => prev.map(op =>
      op.id === operationId
        ? { ...op, status: 'processing' as const, progress: 0 }
        : op
    ))

    try {
      const operation = operations.find(op => op.id === operationId)
      if (!operation) throw new Error('Operation not found')

      const result = await mergePDFs(
        operation.files,
        operation.settings,
        (progress) => {
          setOperations(prev => prev.map(op =>
            op.id === operationId
              ? { ...op, progress }
              : op
          ))
        }
      )

      // Create download URL
      const mergedPdf = await PDFDocument.create()
      for (const pdfFile of operation.files) {
        if (!pdfFile.isValid) continue
        const arrayBuffer = await pdfFile.file.arrayBuffer()
        const sourcePdf = await PDFDocument.load(arrayBuffer)
        const selectedPages = pdfFile.pages?.filter(page => page.selected).map(page => page.pageNumber - 1) || []
        if (selectedPages.length > 0) {
          const copiedPages = await mergedPdf.copyPages(sourcePdf, selectedPages)
          copiedPages.forEach(page => mergedPdf.addPage(page))
        }
      }

      const pdfBytes = await mergedPdf.save()
      const blob = new Blob([pdfBytes], { type: 'application/pdf' })
      const downloadUrl = URL.createObjectURL(blob)

      setOperations(prev => prev.map(op =>
        op.id === operationId
          ? {
              ...op,
              status: 'completed' as const,
              progress: 100,
              result: { ...result, downloadUrl },
              completedAt: new Date()
            }
          : op
      ))

      return { ...result, downloadUrl }
    } catch (error) {
      setOperations(prev => prev.map(op =>
        op.id === operationId
          ? {
              ...op,
              status: 'failed' as const,
              error: error instanceof Error ? error.message : 'Merge failed'
            }
          : op
      ))
      throw error
    }
  }, [operations])

  const removeOperation = useCallback((operationId: string) => {
    setOperations(prev => {
      const operation = prev.find(op => op.id === operationId)
      if (operation?.result?.downloadUrl) {
        URL.revokeObjectURL(operation.result.downloadUrl)
      }
      return prev.filter(op => op.id !== operationId)
    })
  }, [])

  const clearOperations = useCallback(() => {
    operations.forEach(operation => {
      if (operation.result?.downloadUrl) {
        URL.revokeObjectURL(operation.result.downloadUrl)
      }
    })
    setOperations([])
  }, [operations])

  return {
    operations,
    createMergeOperation,
    processMergeOperation,
    removeOperation,
    clearOperations
  }
}

// File processing hook
const usePDFFileProcessor = () => {
  const [files, setFiles] = useState<PDFFile[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const addFiles = useCallback(async (fileList: FileList | File[]) => {
    setIsProcessing(true)
    const newFiles: PDFFile[] = []

    try {
      const fileArray = Array.from(fileList)

      for (const file of fileArray) {
        const validation = validatePDFFile(file)
        if (!validation.isValid) {
          toast.error(`${file.name}: ${validation.errors[0]?.message}`)
          continue
        }

        if (validation.warnings.length > 0) {
          validation.warnings.forEach(warning => {
            toast.warning(`${file.name}: ${warning}`)
          })
        }

        const pdfFile = await processPDFFile(file)
        newFiles.push(pdfFile)

        if (!pdfFile.isValid) {
          toast.error(`Failed to process ${file.name}: ${pdfFile.error}`)
        }
      }

      setFiles(prev => [...prev, ...newFiles])

      if (newFiles.length > 0) {
        toast.success(`Added ${newFiles.length} PDF file(s)`)
      }
    } catch (error) {
      toast.error('Failed to process files')
      console.error(error)
    } finally {
      setIsProcessing(false)
    }
  }, [])

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId))
  }, [])

  const updateFile = useCallback((fileId: string, updates: Partial<PDFFile>) => {
    setFiles(prev => prev.map(file =>
      file.id === fileId ? { ...file, ...updates } : file
    ))
  }, [])

  const reorderFiles = useCallback((startIndex: number, endIndex: number) => {
    setFiles(prev => {
      const result = Array.from(prev)
      const [removed] = result.splice(startIndex, 1)
      result.splice(endIndex, 0, removed)
      return result
    })
  }, [])

  const clearFiles = useCallback(() => {
    setFiles([])
  }, [])

  const togglePageSelection = useCallback((fileId: string, pageNumber: number) => {
    setFiles(prev => prev.map(file => {
      if (file.id === fileId && file.pages) {
        return {
          ...file,
          pages: file.pages.map(page =>
            page.pageNumber === pageNumber
              ? { ...page, selected: !page.selected }
              : page
          )
        }
      }
      return file
    }))
  }, [])

  const selectAllPages = useCallback((fileId: string, selected: boolean) => {
    setFiles(prev => prev.map(file => {
      if (file.id === fileId && file.pages) {
        return {
          ...file,
          pages: file.pages.map(page => ({ ...page, selected }))
        }
      }
      return file
    }))
  }, [])

  return {
    files,
    isProcessing,
    addFiles,
    removeFile,
    updateFile,
    reorderFiles,
    clearFiles,
    togglePageSelection,
    selectAllPages
  }
}

// Copy to clipboard functionality
const useCopyToClipboard = () => {
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const copyToClipboard = useCallback(async (text: string, label?: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(label || 'text')
      toast.success(`${label || 'Text'} copied to clipboard`)

      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedText(null), 2000)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }, [])

  return { copyToClipboard, copiedText }
}
