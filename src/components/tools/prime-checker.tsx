import { useState } from 'react'

/**
 * PrimeChecker - 判断质数
 * @returns 组件
 */
// 输入数字，判断是否为质数
const PrimeChecker = () => {
  const [n, setN] = useState(2)
  const isPrime = (num: number) => {
    if (num < 2) return false
    for (let i = 2; i <= Math.sqrt(num); i++) if (num % i === 0) return false
    return true
  }
  return (
    <div className="flex flex-col gap-4">
      <input
        type="number"
        value={n}
        onChange={(e) => setN(Number(e.target.value))}
        className="w-32 border rounded px-2"
      />
      <div className="text-sm text-muted-foreground">
        {n} {isPrime(n) ? '是质数/Prime' : '不是质数/Not prime'}
      </div>
    </div>
  )
}

export default PrimeChecker
