import { useState } from 'react'

/**
 * WeekNumber - ISO 周数
 * @returns 组件
 */
// 输入日期，输出 ISO 周数
const WeekNumber = () => {
  const [date, setDate] = useState('')
  const [week, setWeek] = useState('')
  const calc = () => {
    const d = new Date(date)
    if (isNaN(d.getTime())) return setWeek('无效日期')
    d.setHours(0, 0, 0, 0)
    d.setDate(d.getDate() + 4 - (d.getDay() || 7))
    const yearStart = new Date(d.getFullYear(), 0, 1)
    const weekNo = Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7)
    setWeek(weekNo.toString())
  }
  return (
    <div className="flex flex-col gap-4">
      <input
        className="w-full rounded border p-2"
        placeholder="日期 YYYY-MM-DD"
        value={date}
        onChange={(e) => setDate(e.target.value)}
      />
      <button className="btn" onClick={calc}>
        计算/Calc
      </button>
      <div className="text-sm text-muted-foreground">ISO 周数/Week: {week}</div>
    </div>
  )
}

export default WeekNumber
