import React, { useState } from 'react'
/**
 * Markdown + Mermaid 渲染工具
 * Markdown + Mermaid renderer
 * 支持输入 Markdown 并渲染 Mermaid 流程图，纯前端实现。
 */
import mermaid from 'mermaid'

const MarkdownMermaid: React.FC = () => {
  const [input, setInput] = useState('')
  const [svg, setSvg] = useState('')
  const [error, setError] = useState('')

  /**
   * 渲染 Mermaid 图表
   * Render Mermaid diagram
   */
  const handleRender = async () => {
    const code = input.match(/```mermaid([\s\S]*?)```/)
    if (!code) {
      setError('未检测到 Mermaid 代码块 / No Mermaid code block found')
      setSvg('')
      return
    }
    try {
      const { svg } = await mermaid.render('mermaid-svg', code[1])
      setSvg(svg)
      setError('')
    } catch (e) {
      setError('渲染失败 / Ren<PERSON> failed')
      setSvg('')
    }
  }

  return (
    <div className="space-y-4">
      <textarea
        className="w-full h-32 p-2 border rounded dark:bg-zinc-900 dark:text-white"
        placeholder="输入 Markdown，包含 ```mermaid 代码块 / Enter Markdown with ```mermaid code block"
        value={input}
        onChange={(e) => setInput(e.target.value)}
      />
      <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700" onClick={handleRender}>
        渲染 / Render
      </button>
      {error && <div className="text-red-600 dark:text-red-400">{error}</div>}
      {svg && <div className="overflow-x-auto" dangerouslySetInnerHTML={{ __html: svg }} />}
    </div>
  )
}

export default MarkdownMermaid
