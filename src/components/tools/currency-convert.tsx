import { useState } from 'react'

/**
 * CurrencyConvert - 静态汇率换算
 * @returns 组件
 */
// 仅支持 USD↔CNY 静态汇率
const rate = 7.0
const CurrencyConvert = () => {
  const [usd, setUsd] = useState(1)
  const [cny, setCny] = useState(usd * rate)
  const toCny = () => setCny(usd * rate)
  const toUsd = () => setUsd(cny / rate)
  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 items-center">
        <input
          type="number"
          value={usd}
          onChange={(e) => setUsd(Number(e.target.value))}
          className="w-24 border rounded px-2"
        />
        <span>USD</span>
        <button className="btn" onClick={toCny}>
          →
        </button>
        <input
          type="number"
          value={cny}
          onChange={(e) => setCny(Number(e.target.value))}
          className="w-24 border rounded px-2"
        />
        <span>CNY</span>
        <button className="btn" onClick={toUsd}>
          ←
        </button>
      </div>
    </div>
  )
}

export default CurrencyConvert
