import { useState } from 'react'
import { Input } from '@/components/ui/input'

/**
 * AgeCalculator - 计算年龄
 * @returns 组件
 */
// 输入出生日期，计算年龄
const AgeCalculator = () => {
  const [birth, setBirth] = useState('')
  const [age, setAge] = useState('')
  const calc = () => {
    const d = new Date(birth)
    if (isNaN(d.getTime())) return setAge('无效日期')
    const diff = Date.now() - d.getTime()
    setAge(Math.floor(diff / (365.25 * 24 * 3600 * 1000)).toString())
  }
  return (
    <div className="flex flex-col gap-4">
      <Input
        type="date"
        className="w-full rounded border p-2"
        placeholder="出生日期 YYYY-MM-DD"
        value={birth}
        onChange={(e) => setBirth(e.target.value)}
        color="primary"
      />
      <button className="btn" onClick={calc}>
        计算/Calc
      </button>
      <div className="text-sm text-muted-foreground">年龄/Age: {age}</div>
    </div>
  )
}

export default AgeCalculator
