import { useState } from 'react'
import { Input } from '../ui/input'
/**
 * EmailValidator - Email Regex Check
 * @returns 组件
 */
// 输入邮箱，正则校验
const EmailValidator = () => {
  const [email, setEmail] = useState('')
  const valid = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/.test(email)
  return (
    <div className="flex flex-col gap-4">
      <Input
        className="w-full rounded border p-2"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="输入邮箱..."
      />
      <div className="text-sm text-muted-foreground">{valid ? '有效/Valid' : '无效/Invalid'}</div>
    </div>
  )
}
export default EmailValidator
