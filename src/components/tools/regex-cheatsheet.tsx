/**
 * RegexCheatsheet - 正则速查
 * @returns 组件
 */
// 常用正则表达式速查表
const cheats = [
  { desc: '邮箱', regex: '/^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$/' },
  { desc: '手机号', regex: '/^1[3-9]\\d{9}$/' },
  { desc: 'URL', regex: '/^(https?):\\/\\/[^\\s]+$/' },
  { desc: '邮编', regex: '/^\\d{6}$/' },
]
const RegexCheatsheet = () => (
  <div className="flex flex-col gap-2">
    {cheats.map((c) => (
      <div key={c.desc} className="flex gap-2 text-sm">
        <span>{c.desc}:</span>
        <span className="font-mono select-all">{c.regex}</span>
      </div>
    ))}
  </div>
)
export default RegexCheatsheet
