/**
 * BarcodeGenerator - 条形码生成
 * @returns 组件
 */
import { useState } from 'react'
import Barcode, { BarcodeProps } from 'react-barcode'
import { Input } from '@/components/ui/input'

const formatOptions: BarcodeProps['format'][] = [
  'CODE128',
  'EAN13',
  'EAN8',
  'UPC',
  'CODE39',
  'ITF14',
  'MSI',
  'pharmacode',
]

const BarcodeGenerator = () => {
  const [value, setValue] = useState('123456789012')
  const [format, setFormat] = useState<BarcodeProps['format']>('CODE128')
  const [width, setWidth] = useState(2)
  const [height, setHeight] = useState(100)
  const [displayValue, setDisplayValue] = useState(true)

  return (
    <div className="max-w-md mx-auto space-y-6">
      <div className="space-y-2">
        <label className="block text-sm font-medium text-[var(--color-text)]">条形码内容</label>
        <Input
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="请输入条形码内容"
          color="primary"
        />
      </div>
      <div className="flex gap-4 flex-wrap">
        <div>
          <label className="block text-xs text-[var(--color-text-secondary)]">格式</label>
          <select
            className="border rounded px-2 py-1 text-sm"
            value={format}
            onChange={(e) => setFormat(e.target.value as BarcodeProps['format'])}
          >
            {formatOptions.map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-xs text-[var(--color-text-secondary)]">宽度</label>
          <Input
            type="number"
            min={1}
            max={10}
            value={width}
            onChange={(e) => setWidth(Number(e.target.value))}
            className="w-20"
          />
        </div>
        <div>
          <label className="block text-xs text-[var(--color-text-secondary)]">高度</label>
          <Input
            type="number"
            min={20}
            max={300}
            value={height}
            onChange={(e) => setHeight(Number(e.target.value))}
            className="w-24"
          />
        </div>
        <div className="flex items-center mt-5">
          <Input
            type="checkbox"
            checked={displayValue}
            onChange={(e) => setDisplayValue(e.target.checked)}
            id="displayValue"
            className="mr-1"
            color="primary"
          />
          <label htmlFor="displayValue" className="text-xs text-[var(--color-text-secondary)]">
            显示文本
          </label>
        </div>
      </div>
      <div className="flex justify-center bg-white p-4 rounded shadow">
        {value ? (
          <Barcode
            value={value}
            format={format}
            width={width}
            height={height}
            displayValue={displayValue}
            background="#fff"
            lineColor="#222"
          />
        ) : (
          <span className="text-[var(--color-text-secondary)]">请输入内容</span>
        )}
      </div>
    </div>
  )
}

export default BarcodeGenerator
