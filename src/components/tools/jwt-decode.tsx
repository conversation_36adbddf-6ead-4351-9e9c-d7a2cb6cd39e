import { useState } from 'react'

/**
 * JwtDecode - 解析 JWT
 * @returns 组件
 */
// 输入 JWT，解析 payload
const JwtDecode = () => {
  const [jwt, setJwt] = useState('')
  let payload = ''
  try {
    payload = JSON.stringify(JSON.parse(atob(jwt.split('.')[1])), null, 2)
  } catch {
    payload = '格式错误/Invalid JWT'
  }
  return (
    <div className="flex flex-col gap-4">
      <textarea
        className="w-full min-h-[60px] rounded border p-2"
        value={jwt}
        onChange={(e) => setJwt(e.target.value)}
        placeholder="输入 JWT..."
      />
      <div className="bg-muted p-2 rounded text-xs font-mono whitespace-pre-line">{payload}</div>
    </div>
  )
}

export default JwtDecode
