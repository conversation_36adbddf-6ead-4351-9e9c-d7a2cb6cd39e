import { useState } from 'react'

/**
 * RomanNumeral - 罗马数字转换
 * @returns 组件
 */
// 阿拉伯数字与罗马数字互转
function toRoman(num: number) {
  const map = [
    [1000, 'M'],
    [900, 'CM'],
    [500, 'D'],
    [400, 'CD'],
    [100, 'C'],
    [90, 'XC'],
    [50, 'L'],
    [40, 'XL'],
    [10, 'X'],
    [9, 'IX'],
    [5, 'V'],
    [4, 'IV'],
    [1, 'I'],
  ]
  let res = ''
  for (const [n, s] of map) {
    // 确保 n 是 number 类型
    const value = n as number
    while (num >= value) {
      res += s
      num -= value
    }
  }
  return res
}
function fromRoman(str: string) {
  const map: Record<string, number> = {
    M: 1000,
    CM: 900,
    D: 500,
    CD: 400,
    C: 100,
    XC: 90,
    L: 50,
    XL: 40,
    X: 10,
    IX: 9,
    V: 5,
    IV: 4,
    I: 1,
  }
  let i = 0,
    num = 0
  while (i < str.length) {
    if (i + 1 < str.length && map[str.slice(i, i + 2)]) {
      num += map[str.slice(i, i + 2)]
      i += 2
    } else {
      num += map[str[i]]
      i++
    }
  }
  return num
}
const RomanNumeral = () => {
  const [arabic, setArabic] = useState(1)
  const [roman, setRoman] = useState(toRoman(arabic))
  return (
    <div className="flex flex-col gap-4">
      <input
        type="number"
        value={arabic}
        onChange={(e) => {
          const v = Number(e.target.value)
          setArabic(v)
          setRoman(toRoman(v))
        }}
        className="w-32 border rounded px-2"
      />
      <input
        value={roman}
        onChange={(e) => {
          setRoman(e.target.value.toUpperCase())
          setArabic(fromRoman(e.target.value.toUpperCase()))
        }}
        className="w-32 border rounded px-2"
      />
      <div className="text-sm text-muted-foreground">
        阿拉伯/Arabic: {arabic} 罗马/Roman: {roman}
      </div>
    </div>
  )
}

export default RomanNumeral
