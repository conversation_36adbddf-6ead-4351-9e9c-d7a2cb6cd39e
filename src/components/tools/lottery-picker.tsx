import { useState } from 'react'
/**
 * LotteryPicker - 抽奖器
 * @returns 组件
 */
// 输入候选项，随机抽取
const LotteryPicker = () => {
  const [input, setInput] = useState('')
  const [result, setResult] = useState('')
  const pick = () => {
    const arr = input.split(/[,，;；\s]+/).filter(Boolean)
    setResult(arr.length ? arr[Math.floor(Math.random() * arr.length)] : '')
  }
  return (
    <div className="flex flex-col gap-4 items-center">
      <textarea
        className="w-full min-h-[60px] rounded border p-2"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="输入候选项，逗号分隔..."
      />
      <button className="btn" onClick={pick}>
        抽取/Pick
      </button>
      <div className="font-mono text-lg select-all">{result}</div>
    </div>
  )
}
export default LotteryPicker
