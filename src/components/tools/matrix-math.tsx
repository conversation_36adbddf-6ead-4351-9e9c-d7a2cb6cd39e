import { useState } from 'react'

/**
 * MatrixMath - 矩阵运算
 * @returns 组件
 */
// 输入 2x2 矩阵，输出和与积
const MatrixMath = () => {
  const [a, setA] = useState('1,2;3,4')
  const [b, setB] = useState('5,6;7,8')
  const parse = (s: string) => s.split(';').map((r) => r.split(',').map(Number))
  let sum = '',
    prod = ''
  try {
    const m1 = parse(a),
      m2 = parse(b)
    const s = [
      [m1[0][0] + m2[0][0], m1[0][1] + m2[0][1]],
      [m1[1][0] + m2[1][0], m1[1][1] + m2[1][1]],
    ]
    sum = `[[${s[0][0]},${s[0][1]}],[${s[1][0]},${s[1][1]}]]`
    const p = [
      [m1[0][0] * m2[0][0] + m1[0][1] * m2[1][0], m1[0][0] * m2[0][1] + m1[0][1] * m2[1][1]],
      [m1[1][0] * m2[0][0] + m1[1][1] * m2[1][0], m1[1][0] * m2[0][1] + m1[1][1] * m2[1][1]],
    ]
    prod = `[[${p[0][0]},${p[0][1]}],[${p[1][0]},${p[1][1]}]]`
  } catch {
    sum = prod = '格式错误'
  }
  return (
    <div className="flex flex-col gap-4">
      <input
        className="w-full rounded border p-2"
        value={a}
        onChange={(e) => setA(e.target.value)}
        placeholder="1,2;3,4"
      />
      <input
        className="w-full rounded border p-2"
        value={b}
        onChange={(e) => setB(e.target.value)}
        placeholder="5,6;7,8"
      />
      <div className="text-sm text-muted-foreground">和/Sum: {sum}</div>
      <div className="text-sm text-muted-foreground">积/Product: {prod}</div>
    </div>
  )
}

export default MatrixMath
