import { useState } from 'react'
/**
 * FakeUser - 虚拟人资料
 * @returns 组件
 */
// 随机生成简单虚拟人资料
const names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
const FakeUser = () => {
  const [user, setUser] = useState({
    name: names[Math.floor(Math.random() * names.length)],
    age: Math.floor(Math.random() * 40) + 18,
    email: '<EMAIL>',
  })
  const gen = () =>
    setUser({
      name: names[Math.floor(Math.random() * names.length)],
      age: Math.floor(Math.random() * 40) + 18,
      email: '<EMAIL>',
    })
  return (
    <div className="flex flex-col gap-4 items-center">
      <div>姓名/Name: {user.name}</div>
      <div>年龄/Age: {user.age}</div>
      <div>邮箱/Email: {user.email}</div>
      <button className="btn" onClick={gen}>
        生成/Generate
      </button>
    </div>
  )
}
export default FakeUser
