import { useState } from 'react'
import { nanoid } from 'nanoid'

const UuidGenerator = () => {
  const [uuid, setUuid] = useState(nanoid(16))
  const gen = () => setUuid(nanoid(16))
  return (
    <div className="flex flex-col gap-4 items-center">
      <div className="font-mono text-lg select-all">{uuid}</div>
      <button className="btn" onClick={gen}>
        生成/Generate
      </button>
    </div>
  )
}

export default UuidGenerator
