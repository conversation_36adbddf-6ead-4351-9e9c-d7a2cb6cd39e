import { useState } from 'react'

/**
 * WorkingDays - 工作日计算
 * @returns 组件
 */
// 输入起止日期，计算工作日天数
const WorkingDays = () => {
  const [a, setA] = useState('')
  const [b, setB] = useState('')
  const [days, setDays] = useState('')
  const calc = () => {
    const d1 = new Date(a)
    const d2 = new Date(b)
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return setDays('无效日期')
    let count = 0
    for (let d = new Date(d1); d <= d2; d.setDate(d.getDate() + 1)) {
      const day = d.getDay()
      if (day !== 0 && day !== 6) count++
    }
    setDays(count.toString())
  }
  return (
    <div className="flex flex-col gap-4">
      <input
        className="w-full rounded border p-2"
        placeholder="起始日期 YYYY-MM-DD"
        value={a}
        onChange={(e) => setA(e.target.value)}
      />
      <input
        className="w-full rounded border p-2"
        placeholder="结束日期 YYYY-MM-DD"
        value={b}
        onChange={(e) => setB(e.target.value)}
      />
      <button className="btn" onClick={calc}>
        计算/Calc
      </button>
      <div className="text-sm text-muted-foreground">工作日天数/Days: {days}</div>
    </div>
  )
}

export default WorkingDays
