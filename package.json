{"name": "kit", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.82.0", "@tanstack/react-query-devtools": "^5.82.0", "@tanstack/react-router": "^1.125.6", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-updater": "^2.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gifuct-js": "^2.1.2", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "mermaid": "^11.8.1", "motion": "^12.23.2", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-barcode": "^1.6.1", "react-dom": "^18.3.1", "recharts": "^3.0.2", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.75"}, "devDependencies": {"@tanstack/router-plugin": "^1.125.6", "@tauri-apps/cli": "^2", "@types/node": "^24.0.10", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "tw-animate-css": "^1.3.5", "typescript": "~5.6.2", "vite": "^6.0.3"}}