[profile.dev]
incremental = true

[profile.release]
codegen-units = 1 # 允许 LLVM 执行更好的优化。
lto = true # 启用链接时优化。
opt-level = "s" # 优先考虑小的二进制文件大小。如果您更喜欢速度，请使用 `3`。
panic = "abort" # 通过禁用 panic 处理程序来提高性能。
strip = true # 确保移除调试符号。

[package]
name = "kit"
version = "0.0.1"
description = "Developers Tool"
authors = ["manon"]
edition = "2021"


[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "kit_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-process = "2"
tauri-plugin-dialog = "2"

[target.'cfg(any(target_os = "macos", windows, target_os = "linux"))'.dependencies]
tauri-plugin-updater = "2.9.0"

