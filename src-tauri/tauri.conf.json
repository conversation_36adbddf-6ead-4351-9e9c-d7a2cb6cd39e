{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "0.1.0", "identifier": "icu.kit.manon", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 1200, "height": 800}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "<PERSON>", "createUpdaterArtifacts": true}, "plugins": {"updater": {"active": true, "endpoints": ["https://api.github.com/repos/kit-app/kit/releases/latest"], "dialog": true, "pubkey": "pubkey", "windows": {"installMode": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDNFM0ZFMjEyQzJBQTQ3QzMKUldURFI2ckNFdUkvUGg4Z2FlYUZ5ZGVYaE0xSkZtdmFBYmlCQkNLUUJuZUdOUFkyb3ZJYXdQczYK%"}}}}