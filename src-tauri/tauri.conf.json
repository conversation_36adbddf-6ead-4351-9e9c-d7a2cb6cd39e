{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "0.0.1", "identifier": "icu.kit.manon", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 1440, "height": 900}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "<PERSON>", "createUpdaterArtifacts": true, "windows": {"webviewInstallMode": {"type": "skip"}, "nsis": {"compression": "none"}}}, "plugins": {"updater": {"dialog": true, "active": true, "endpoints": ["https://github.com/aafnnp/kit/releases/latest/download/latest.json"], "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDNFM0ZFMjEyQzJBQTQ3QzMKUldURFI2ckNFdUkvUGg4Z2FlYUZ5ZGVYaE0xSkZtdmFBYmlCQkNLUUJuZUdOUFkyb3ZJYXdQczYK", "windows": {"installMode": "quiet", "installerArgs": ["/NS"]}}}}