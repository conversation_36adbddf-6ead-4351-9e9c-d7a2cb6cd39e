{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "0.1.0", "identifier": "icu.kit.manon", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 1200, "height": 800}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "<PERSON>"}}