// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		12546211D634AFBBBF657813 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5032015EE696252EF65774C1 /* CoreGraphics.framework */; };
		1FE111B34BDA9F9A0F02743F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 850DB89ABABCA1A8E2BD0410 /* LaunchScreen.storyboard */; };
		2AD13C89E5F537944C539870 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A21BCDE23C42EB15D0291BDD /* WebKit.framework */; };
		3708B071F6612015DD1A2928 /* assets in Resources */ = {isa = PBXBuildFile; fileRef = 4F00F4519010BCB45FD5015F /* assets */; };
		5356B20A4855E7AB32C6D825 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = AA55767172119600AA06B0B6 /* main.mm */; };
		71AE06D471F32D71570D9C77 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AE789BFC003477DDE73ACFA2 /* Security.framework */; };
		BD0C5432C6B1609F4968FA96 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E2310BCC35AE60B04CD62FF4 /* Metal.framework */; };
		CAC23B617ADBAB84541C5786 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D02FE485751F9AE45338EB34 /* UIKit.framework */; };
		D81EDC9ACFB0135894A16D6C /* libapp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1133C23B099D03529840B2AA /* libapp.a */; };
		F24099422179599D525AFD76 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 93A55603CB28FA50409463E5 /* MetalKit.framework */; };
		F82431236D40F259FBFC6F5A /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CD26EC00997EF118B38EF82C /* QuartzCore.framework */; };
		F9450F62BEE9338357AE49B9 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = AB779E148AD1B61CE85A74C0 /* Assets.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0CA2F7BF370ED1D5963D034C /* lib.rs */ = {isa = PBXFileReference; path = lib.rs; sourceTree = "<group>"; };
		1133C23B099D03529840B2AA /* libapp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libapp.a; sourceTree = "<group>"; };
		4222FEF26B03B363990BA08A /* kit_iOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = kit_iOS.entitlements; sourceTree = "<group>"; };
		4F00F4519010BCB45FD5015F /* assets */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assets; sourceTree = SOURCE_ROOT; };
		5032015EE696252EF65774C1 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		67BB51C5329815A9D900B54C /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		6B460E585A5A8A8C7F4E397B /* bindings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bindings.h; sourceTree = "<group>"; };
		7ECFAC76CBA6F581BD8261D6 /* kit_iOS.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = kit_iOS.app; sourceTree = BUILT_PRODUCTS_DIR; };
		850DB89ABABCA1A8E2BD0410 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		93A55603CB28FA50409463E5 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		A21BCDE23C42EB15D0291BDD /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		AA55767172119600AA06B0B6 /* main.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		AB779E148AD1B61CE85A74C0 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		AE789BFC003477DDE73ACFA2 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		CD26EC00997EF118B38EF82C /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		D02FE485751F9AE45338EB34 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		E2310BCC35AE60B04CD62FF4 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		EF5D272FD4C54C14C1E2F3DA /* main.rs */ = {isa = PBXFileReference; path = main.rs; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9A6AFD6386FCBE0FB8C3CD40 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D81EDC9ACFB0135894A16D6C /* libapp.a in Frameworks */,
				12546211D634AFBBBF657813 /* CoreGraphics.framework in Frameworks */,
				BD0C5432C6B1609F4968FA96 /* Metal.framework in Frameworks */,
				F24099422179599D525AFD76 /* MetalKit.framework in Frameworks */,
				F82431236D40F259FBFC6F5A /* QuartzCore.framework in Frameworks */,
				71AE06D471F32D71570D9C77 /* Security.framework in Frameworks */,
				CAC23B617ADBAB84541C5786 /* UIKit.framework in Frameworks */,
				2AD13C89E5F537944C539870 /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1CC907553D9CEC3FC4053259 = {
			isa = PBXGroup;
			children = (
				4F00F4519010BCB45FD5015F /* assets */,
				AB779E148AD1B61CE85A74C0 /* Assets.xcassets */,
				850DB89ABABCA1A8E2BD0410 /* LaunchScreen.storyboard */,
				917DD0A1F72B964DC06BB0C3 /* Externals */,
				ECDBAE1B535B8005F599F18E /* kit_iOS */,
				C1D01D26CE438E2921C92858 /* Sources */,
				C4FF0C83162125FD05A50A7B /* src */,
				282A16D376CB7ECF3B9DCD5F /* Frameworks */,
				929866D3B17B6D36B4010D0E /* Products */,
			);
			sourceTree = "<group>";
		};
		282A16D376CB7ECF3B9DCD5F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5032015EE696252EF65774C1 /* CoreGraphics.framework */,
				1133C23B099D03529840B2AA /* libapp.a */,
				E2310BCC35AE60B04CD62FF4 /* Metal.framework */,
				93A55603CB28FA50409463E5 /* MetalKit.framework */,
				CD26EC00997EF118B38EF82C /* QuartzCore.framework */,
				AE789BFC003477DDE73ACFA2 /* Security.framework */,
				D02FE485751F9AE45338EB34 /* UIKit.framework */,
				A21BCDE23C42EB15D0291BDD /* WebKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3F950659879D76CE1BB229F4 /* bindings */ = {
			isa = PBXGroup;
			children = (
				6B460E585A5A8A8C7F4E397B /* bindings.h */,
			);
			path = bindings;
			sourceTree = "<group>";
		};
		917DD0A1F72B964DC06BB0C3 /* Externals */ = {
			isa = PBXGroup;
			children = (
			);
			path = Externals;
			sourceTree = "<group>";
		};
		929866D3B17B6D36B4010D0E /* Products */ = {
			isa = PBXGroup;
			children = (
				7ECFAC76CBA6F581BD8261D6 /* kit_iOS.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		ACDD74EBDF5EA786EE252D4D /* kit */ = {
			isa = PBXGroup;
			children = (
				AA55767172119600AA06B0B6 /* main.mm */,
				3F950659879D76CE1BB229F4 /* bindings */,
			);
			path = kit;
			sourceTree = "<group>";
		};
		C1D01D26CE438E2921C92858 /* Sources */ = {
			isa = PBXGroup;
			children = (
				ACDD74EBDF5EA786EE252D4D /* kit */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		C4FF0C83162125FD05A50A7B /* src */ = {
			isa = PBXGroup;
			children = (
				0CA2F7BF370ED1D5963D034C /* lib.rs */,
				EF5D272FD4C54C14C1E2F3DA /* main.rs */,
			);
			name = src;
			path = ../../src;
			sourceTree = "<group>";
		};
		ECDBAE1B535B8005F599F18E /* kit_iOS */ = {
			isa = PBXGroup;
			children = (
				67BB51C5329815A9D900B54C /* Info.plist */,
				4222FEF26B03B363990BA08A /* kit_iOS.entitlements */,
			);
			path = kit_iOS;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7151B9E5B529DFD20BA17C5A /* kit_iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 50108FFD158A5081CF35E701 /* Build configuration list for PBXNativeTarget "kit_iOS" */;
			buildPhases = (
				A1F4ABDAF2322FC1A61E4BB8 /* Build Rust Code */,
				C6064960E0959E92B8348E88 /* Sources */,
				66F1FCCF5E0E68E4DCE3925F /* Resources */,
				9A6AFD6386FCBE0FB8C3CD40 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = kit_iOS;
			packageProductDependencies = (
			);
			productName = kit_iOS;
			productReference = 7ECFAC76CBA6F581BD8261D6 /* kit_iOS.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		63F62DC236F2D5071131EDCC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
				};
			};
			buildConfigurationList = D4C242C7EC0557D5D477404B /* Build configuration list for PBXProject "kit" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = 1CC907553D9CEC3FC4053259;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 54;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7151B9E5B529DFD20BA17C5A /* kit_iOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		66F1FCCF5E0E68E4DCE3925F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F9450F62BEE9338357AE49B9 /* Assets.xcassets in Resources */,
				1FE111B34BDA9F9A0F02743F /* LaunchScreen.storyboard in Resources */,
				3708B071F6612015DD1A2928 /* assets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A1F4ABDAF2322FC1A61E4BB8 /* Build Rust Code */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Build Rust Code";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(SRCROOT)/Externals/x86_64/${CONFIGURATION}/libapp.a",
				"$(SRCROOT)/Externals/arm64/${CONFIGURATION}/libapp.a",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "npm run -- tauri ios xcode-script -v --platform ${PLATFORM_DISPLAY_NAME:?} --sdk-root ${SDKROOT:?} --framework-search-paths \"${FRAMEWORK_SEARCH_PATHS:?}\" --header-search-paths \"${HEADER_SEARCH_PATHS:?}\" --gcc-preprocessor-definitions \"${GCC_PREPROCESSOR_DEFINITIONS:-}\" --configuration ${CONFIGURATION:?} ${FORCE_COLOR} ${ARCHS:?}";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C6064960E0959E92B8348E88 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5356B20A4855E7AB32C6D825 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		01916305932E26D90940B49A /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = debug;
		};
		23DBC4EF79CF3410918FA129 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = release;
		};
		C4105129C8A2379EB1D25044 /* debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = kit_iOS/kit_iOS.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = kit_iOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = icu.kit.manon;
				PRODUCT_NAME = "kit";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = debug;
		};
		F4667F18295C7CF386FE3052 /* release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ARCHS = (
					arm64,
				);
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = kit_iOS/kit_iOS.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphoneos*]" = x86_64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\".\"",
				);
				INFOPLIST_FILE = kit_iOS/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				"LIBRARY_SEARCH_PATHS[arch=arm64]" = "$(inherited) $(PROJECT_DIR)/Externals/arm64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				"LIBRARY_SEARCH_PATHS[arch=x86_64]" = "$(inherited) $(PROJECT_DIR)/Externals/x86_64/$(CONFIGURATION) $(SDKROOT)/usr/lib/swift $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME) $(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)";
				PRODUCT_BUNDLE_IDENTIFIER = icu.kit.manon;
				PRODUCT_NAME = "kit";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALID_ARCHS = arm64;
			};
			name = release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		50108FFD158A5081CF35E701 /* Build configuration list for PBXNativeTarget "kit_iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C4105129C8A2379EB1D25044 /* debug */,
				F4667F18295C7CF386FE3052 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
		D4C242C7EC0557D5D477404B /* Build configuration list for PBXProject "kit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				01916305932E26D90940B49A /* debug */,
				23DBC4EF79CF3410918FA129 /* release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 63F62DC236F2D5071131EDCC /* Project object */;
}
