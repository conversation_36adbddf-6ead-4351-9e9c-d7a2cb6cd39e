#!/bin/bash

# Enhanced Tools Test Suite Runner
# Comprehensive testing script for all enhanced tools

set -e

echo "🚀 Enhanced Tools Test Suite"
echo "============================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    print_warning "Dependencies not found. Installing..."
    npm install
fi

# Create test reports directory
mkdir -p test-reports

print_status "Starting comprehensive test suite..."
echo ""

# 1. Run unit tests for individual tools
print_status "Running unit tests for enhanced tools..."
if npm run test:run src/test/tools/ 2>/dev/null; then
    print_success "Unit tests passed"
else
    print_warning "Some unit tests failed - check output above"
fi
echo ""

# 2. Run accessibility tests
print_status "Running accessibility tests..."
if npm run test:run src/test/accessibility.test.tsx 2>/dev/null; then
    print_success "Accessibility tests passed"
else
    print_warning "Some accessibility tests failed - check output above"
fi
echo ""

# 3. Run performance tests
print_status "Running performance tests..."
if npm run test:run src/test/performance.test.tsx 2>/dev/null; then
    print_success "Performance tests passed"
else
    print_warning "Some performance tests failed - check output above"
fi
echo ""

# 4. Run integration tests
print_status "Running integration tests..."
if npm run test:run src/test/tools/all-tools.test.tsx 2>/dev/null; then
    print_success "Integration tests passed"
else
    print_warning "Some integration tests failed - check output above"
fi
echo ""

# 5. Run coverage analysis
print_status "Generating coverage report..."
if npm run test:coverage 2>/dev/null; then
    print_success "Coverage report generated"
else
    print_warning "Coverage analysis failed"
fi
echo ""

# 6. Generate comprehensive report
print_status "Generating comprehensive test report..."
if npx tsx src/test/test-runner.ts 2>/dev/null; then
    print_success "Comprehensive report generated"
else
    print_warning "Report generation failed"
fi
echo ""

# 7. Check for common issues
print_status "Checking for common issues..."

# Check TypeScript compilation
if npx tsc --noEmit 2>/dev/null; then
    print_success "TypeScript compilation check passed"
else
    print_warning "TypeScript compilation issues found"
fi

# Check for console errors in tests
if grep -r "console.error" src/test/ >/dev/null 2>&1; then
    print_warning "Console errors found in test output"
fi

# Check test file coverage
TOOL_COUNT=$(find src/components/tools -name "*.tsx" | wc -l)
TEST_COUNT=$(find src/test/tools -name "*.test.tsx" | wc -l)

if [ $TEST_COUNT -lt $((TOOL_COUNT / 2)) ]; then
    print_warning "Test coverage may be insufficient - consider adding more test files"
fi

echo ""
print_status "Test Summary:"
echo "============="
echo "✅ Unit Tests: Individual tool functionality"
echo "♿ Accessibility Tests: ARIA, keyboard navigation, screen readers"
echo "⚡ Performance Tests: Rendering, processing, memory usage"
echo "🔗 Integration Tests: Cross-tool consistency and features"
echo "📊 Coverage Analysis: Code coverage metrics"
echo "📋 Comprehensive Report: Detailed HTML/JSON reports"
echo ""

# Final status
if [ $? -eq 0 ]; then
    print_success "All tests completed! Check test-reports/ for detailed results."
    echo ""
    echo "📊 View reports:"
    echo "   - HTML Report: Open test-report-*.html in your browser"
    echo "   - JSON Report: test-report-*.json for programmatic access"
    echo "   - Coverage Report: coverage/index.html"
    echo ""
    echo "🚀 Next steps:"
    echo "   - Review any failed tests and fix issues"
    echo "   - Check accessibility recommendations"
    echo "   - Monitor performance metrics"
    echo "   - Maintain test coverage above 80%"
else
    print_error "Some tests failed. Please review the output above."
    exit 1
fi
